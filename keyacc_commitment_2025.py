import pyodbc
import openpyxl
import math

# ฟังก์ชันเพื่อดึงค่าสีของเซลล์
def get_cell_color(cell):
    if cell.fill.start_color.index:
        return cell.fill.start_color.index
    return None

# prepare to insert
server = 'tcp:172.18.1.112'
database = 'DW'
username = 'powerbi'
password = 'Nation#2021'
cnxn = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER='+server+';DATABASE='+database+';UID='+username+';PWD='+ password)
cursor = cnxn.cursor()

not_found = []
not_match = []
pyear = 2025
sheet_name = 'COR1'

# เปิดไฟล์ Excel
wb = openpyxl.load_workbook('COR1+COR2 28-3-68.xlsx', data_only=True)

# เลือกแผ่นงาน
# sheet = wb['รวมทุก BU']
sheet = wb[sheet_name]


# ระบุตำแหน่งคอลัมน์ที่ต้องการอ่าน
# cols_to_read = [1] + [2] + list(range(16, 27))
# cols_to_read = [1] + [2] + list(range(17, 28))
cols_to_read = [2] + [3] + list(range(5, 15))

map_brand = {}
# เริ่มอ่านค่า
# for row_num, row in enumerate(sheet.iter_rows(min_row=4, values_only=True), start=4):    
# for row_num, row in enumerate(sheet.iter_rows(min_row=4), start=4):
for row_num, row in enumerate(sheet.iter_rows(min_row=2), start=2):
    # if row_num == 4:
    #     print ("Header Mapping")
    #     for idx, col in enumerate(cols_to_read):
    #         cell_value = row[idx]
    #         if cell_value in mapping:
    #             map_brand[idx] = mapping[cell_value]
    #     print ("Mapping: ", map_brand)
    #     continue
    
    # if row_num == 4:
    if row_num == 2:
        print("Header Mapping")
        for idx, col in enumerate(cols_to_read):
            cell = sheet.cell(row=row_num, column=col)        
            map_brand[idx] = cell.value            
        print("Mapping: ", map_brand)
        continue

    if sheet.row_dimensions[row_num].hidden:
        continue  # ข้ามแถวที่ถูกซ่อน
    
    # ตรวจสอบค่าและพิมพ์ค่าสีของเซลล์
    # color = get_cell_color(row[1])
    # if color:
    #     print(f"Row {row_num}, {row[1].value} Color of cell in column 2: {color}")
    # else:
    #     print(f"Row {row_num}, {row[1].value} Cell in column 2 has no fill color")

    # ข้ามแถวที่มีเซลล์ในคอลัมน์ที่สองเป็นสีส้ม    
    type_commit = 'Direct'
    if row[0].value == 'Grand Total':
        print("End of Grand Total")
        break

    row_values = []
    customer_no = '';
    customer_name = '';
    for idx, col in enumerate(cols_to_read):
        cell = sheet.cell(row=row_num, column=col)        
        # print(idx, cell.value);
        if idx == 0:
            customer_no = cell.value
            # print(type(customer_name) == str)
        elif idx == 1:
            customer_name = cell.value
            # print(type(customer_name) == str)
        else:
            # print (isinstance(cell.value, type(None)))
            # if not(customer_no is None or customer_name is None or isinstance(cell.value, type(None)) or cell.value is None):
            if not(customer_no is None or customer_name is None):
                if idx in map_brand:
                    # if float(cell.value) > 0.0:
                    if (True):
                        print ("No:", customer_no,"Name:", customer_name," Mapping: ", map_brand[idx], " Value: ", cell.value)

                        if customer_no != "":                            
                            # Delete Previous data
                            del_count = cursor.execute("delete KeyAcc_Commitment where year_commit=? and adrno=? and Brand_Code=? and excel_name=? and sheet_name=? ",pyear, customer_no, map_brand[idx], customer_name, sheet_name).rowcount
                            print('KeyAcc_Commitment Rows deleted: ' + str(del_count))
                            # rounded_value = round(cell.value, 2)
                            rounded_value = cell.value
                            ins_count = cursor.execute("""
                                    insert into KeyAcc_Commitment (ADRNO, excel_name, sheet_name, Brand_Code, Commitment, Year_Commit, Type_Commit, excel_row)
                                    values(?, ?, ?, ?, ?,?,?,?)""",customer_no, customer_name, sheet_name, map_brand[idx], rounded_value , pyear, type_commit, row_num).rowcount
                            print('KeyAcc_Commitment Rows inserted: ' + str(del_count))
                            cnxn.commit() 
                        else:
                            if customer_name not in not_found:                                
                                not_found.append(customer_name)                           

        cell = sheet.cell(row=row_num, column=col)
        row_values.append(cell.value)  # เพิ่มค่าจริงของเซลล์ลงในลิสต์
    print(row_values)
print ("Not Match Brand: ")
print (not_match)    
print ("Not Found List: ")
print (not_found)
# ปิด cursor และการเชื่อมต่อ
cursor.close()
cnxn.close()
