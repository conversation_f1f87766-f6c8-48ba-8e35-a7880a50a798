import openpyxl
import pymysql
import re
from sqlalchemy import create_engine
import pandas as pd
import datetime

def thai_to_month_number(thai_month_name):
  """
  ฟังก์ชันแปลงชื่อเดือนภาษาไทยเป็นเลขเดือน

  Args:
      thai_month_name: ชื่อเดือนภาษาไทย (string)

  Returns:
      เลขเดือน (int) หรือ None ถ้าไม่พบชื่อเดือน
  """
  thai_months = {
      "มกราคม": 0,
      "กุมภาพันธ์": 1,
      "มีนาคม": 2,
      "เมษายน": 3,
      "พฤษภาคม": 4,
      "มิถุนายน": 5,
      "กรกฎาคม": 6,
      "สิงหาคม": 7,
      "กันยายน": 8,
      "ตุลาคม": 9,
      "พฤศจิกายน": 10,
      "ธันวาคม": 11
  }

  if thai_month_name in thai_months:
      return thai_months[thai_month_name]
  else:
      return None

def thai_to_month_date(thai_month_name):
  """
  ฟังก์ชันแปลงชื่อเดือนภาษาไทยเป็นเลขเดือน และคืนค่าวันที่ 1 ของเดือนนั้นๆ

  Args:
    thai_month_name: ชื่อเดือนภาษาไทย (string)

  Returns:
    วันที่ 1 ของเดือนที่แปลงมา (datetime.date) หรือ None ถ้าไม่พบชื่อเดือน
  """
  if thai_month_name is None:
      return None

  try:
    month_number = thai_to_month_number(thai_month_name)
  except KeyError:
    return None
  
  # สร้างวัตถุ datetime.date ของวันที่ 1 ของเดือน
  first_day_of_month = datetime.date(year=2024, month=month_number + 1, day=1)
  return first_day_of_month

def month_to_month_date(month_number):

  if month_number is None:
     return None
  
  # สร้างวัตถุ datetime.date ของวันที่ 1 ของเดือน
  first_day_of_month = datetime.date(year=2024, month=month_number + 1, day=1)
  return first_day_of_month


def is_valid_bu(string):
  """
  ฟังก์ชันนี้ตรวจสอบว่าสตริงมีความยาว 12 หลักและประกอบด้วยตัวเลข 0 ถึง 9 เท่านั้น

  Args:
      string: สตริงที่ต้องการตรวจสอบ

  Returns:
      True: สตริงถูกต้อง
      False: สตริงไม่ถูกต้อง
  """
  pattern = r"^[0-9]{12}$"
  result = re.search(pattern, string)
  return bool(result)


# Stage
# db = pymysql.connect(
#     host="**************",
#     user="dev01",
#     password="Dev#!2021",
#     database="db_career"
# )

# Production 
db = pymysql.connect(
    host="**************",
    user="api-career",
    password="nB4fwNUQSaFUG6OT",
    database="db_career"
)

engine = create_engine('oracle://HRMSIT:HRMSIT@************:1521/hrms')

wb = openpyxl.load_workbook('Template_bg.xlsx')

for sheet in wb.worksheets:
    for row in sheet.iter_rows(min_row=2):
        if row[0].value is not None and row[1].value is not None and row[1].value:
            # print("BU: ", row[1].value)
            bu_no = row[1].value.strip()
            # ตรวจสอบว่าคอลัมน์ B มีขนาด 12 ตัวอักษร
            # if len(row[1].value) == 12 and is_valid_bu(row[1].value):
            if len(bu_no) == 12 and is_valid_bu(bu_no):                
                # ดึงข้อมูลจากคอลัมน์
                data = [cell.value for cell in row]
                # selected_data = [cell.value for cell in row if cell.column in [2, 3, 4, 6, 7, 9]]
                # budget_no = data[1][0:6]
                budget_no = bu_no[0:6]
                job_name = data[2].strip()
                budget_value = data[3] or 0.0

                try:
                    quota_fill = int(data[5])
                except  (TypeError, ValueError):
                    quota_fill = 0
                try:
                    quota_new = int(data[6])
                except  (TypeError, ValueError):
                    quota_new = 0

                try:
                    quota_remind = int(data[7])
                except  (TypeError, ValueError):
                    quota_remind = 0
                                
                quota_total = quota_fill + quota_new
                if quota_total == 0:
                    quota_total = 1

                start_month = data[8]
                # Prepare Budget
                budget_month = [0.0] * 12
                month_no = thai_to_month_number(start_month)
                if month_no is not None:
                    for i in range(12):
                        if i < month_no:
                            budget_month[i] = 0.0
                        else:
                            budget_month[i] = budget_value * quota_total
                # Read Acutal
                actual_month = [0.0] * 12
                first_actual_month = None
                sum_actual_salary = 0.0
                avg_actual_salary = None
                for i in range(12):
                    try:
                        actual_month[i] = float(data[9+i])
                        sum_actual_salary += actual_month[i]
                        if first_actual_month is None:
                            first_actual_month = i
                    except  (TypeError, ValueError):
                        actual_month[i] = 0.0
                if first_actual_month is not None:                        
                    avg_actual_salary = sum_actual_salary / (12 - first_actual_month)
                print (budget_no, job_name, budget_value, quota_total, start_month, first_actual_month)                
                print ('budget: ', budget_month)
                print ('actual: ', actual_month)

                if start_month is None:
                    effective_date = month_to_month_date(first_actual_month)
                    budget_month = actual_month
                else:
                    effective_date = thai_to_month_date(start_month)

                # Insert job_names
                sql = """
                    INSERT INTO job_names (name, comp_no, division_no, created_at, created_by, updated_at, updated_by)
                    VALUES (%s,%s,%s,NOW(),70,NOW(),70)
                """                
                cursor = db.cursor()                
                cursor.execute(sql, (job_name, budget_no[0:3], budget_no[3:6]))                
                job_id = cursor.lastrowid
                print ('inserted: ', cursor.rowcount, ' job id:', job_id)
                # Insert header
                
                # check comp_name and comp_code
                sql = """
                    SELECT namcenttha,namcent3 FROM hrms.tcenter where codcomp = :codcomp
                """                
                codcomp = str(budget_no)[0:3].ljust(21, '0')
                params = {'codcomp': f"{codcomp}"}  # Enclose codcomp in quotes
                df = pd.read_sql(sql, engine, params=params)

                first_row = df.iloc[0]
                comp_name = first_row['namcenttha']
                comp_code = first_row['namcent3']
                # check department name
                codcomp = str(budget_no)[0:6].ljust(21, '0')
                params = {'codcomp': f"{codcomp}"}  # Enclose codcomp in quotes
                df = pd.read_sql(sql, engine, params=params)

                first_row = df.iloc[0]
                divi_name = first_row['namcenttha']

                print (comp_code, comp_name, divi_name)

                sql = """
                    INSERT INTO budget_headers (job_id, year, budget_no, comp_no, comp_code, comp_name, division_no, division,
                        budget_jan, budget_feb, budget_mar, budget_apr, budget_may, budget_jun, 
                        budget_jul, budget_aug,	budget_sep, budget_oct, budget_nov, budget_dec,
                        status, created_at, created_by, updated_at, updated_by )
                    VALUES (%s,2024,%s,%s,%s,%s,%s,%s
                        ,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s
                        ,'Active',NOW(),70,NOW(),70)
                """                
                cursor = db.cursor()                
                cursor.execute(sql, (job_id, budget_no[0:6], budget_no[0:3], comp_code, comp_name, budget_no[3:6], divi_name,
                                    budget_month[0], budget_month[1], budget_month[2], budget_month[3], 
                                    budget_month[4], budget_month[5], budget_month[6], budget_month[7], 
                                    budget_month[8], budget_month[9], budget_month[10], budget_month[11]))                
                header_id = cursor.lastrowid
                print ('inserted: ', cursor.rowcount, ' header id:', header_id)

                # Insert Detail                
                sql = """
                    INSERT INTO budget_details (header_id, effective_date, salary, travel_expenses,
                    phone_expenses, created_at, created_by, updated_at, updated_by )

                    VALUES (%s,%s,%s,0.0,0.0,NOW(),70,NOW(),70)
                """                
                cursor = db.cursor()
                for i in range(quota_total):
                    cursor.execute(sql, (header_id, effective_date, budget_value ))                
                    detail_id = cursor.lastrowid
                    print ('inserted: ', cursor.rowcount, ' detail id:', detail_id)

                # Insert Actual
                sql = """
                    INSERT INTO budget_actuals (header_id, year, budget_no, 
                    budget_jan, budget_feb, budget_mar, budget_apr, 
                    budget_may, budget_jun, budget_jul, budget_aug, 
                    budget_sep, budget_oct, budget_nov, budget_dec, 
                    created_at, created_by, updated_at, updated_by )

                    VALUES (%s,2024,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,NOW(),70,NOW(),70)
                """                
                cursor = db.cursor()                
                cursor.execute(sql, (header_id, budget_no[0:6], 
                                    actual_month[0], actual_month[1], actual_month[2], actual_month[3], 
                                    actual_month[4], actual_month[5], actual_month[6], actual_month[7], 
                                    actual_month[8], actual_month[9], actual_month[10], actual_month[11]                                     
                                    ))                
                actual_id = cursor.lastrowid
                print ('inserted: ', cursor.rowcount, ' actual id:', actual_id)

                # Insert Transaction
                if (quota_total - quota_remind) > 0:
                    cursor = db.cursor()                
                    # Reduce Headcount
                    avg_salary = avg_actual_salary / (quota_total - quota_remind)
                    # transaction_type = 'replace' if quota_fill > 0 else 'reduce'
                    transaction_type = 'reduce'
                    for i in range(quota_total - quota_remind):                        
                        
                        sql = """
                            INSERT INTO budget_transactions (budget_id, year, budget_no, 
                            transaction_type, job_id, effective_date, headcount, 
                            budget, salary, salary_probation, created_at, created_id )
                            VALUES (%s,2024,%s,%s,%s,%s,NULL,%s,%s,%s,NOW(),70)
                        """                
                        cursor.execute(sql, (header_id, budget_no[0:6], transaction_type,
                                            job_id, effective_date,  
                                            budget_value, avg_salary, avg_salary
                                            ))                
                        transaction_id = cursor.lastrowid
                        print ('inserted: ', cursor.rowcount, ' transaction id:', transaction_id)
                    # quota_remind                    
                    sql = """
                        INSERT INTO budget_transactions (budget_id, year, budget_no, 
                        transaction_type, job_id, effective_date, headcount, 
                        budget, salary, salary_probation, created_at, created_id )
                        VALUES (%s,2024,%s,%s,%s,%s,%s,NULL,NULL,NULL,NOW(),70)
                    """                
                    cursor.execute(sql, (header_id, budget_no[0:6], 'reduce', 
                                        job_id, effective_date, quota_total - quota_remind
                                        ))                
                    transaction_id = cursor.lastrowid
                    print ('inserted: ', cursor.rowcount, ' transaction id:', transaction_id)

                # # # บันทึกการเปลี่ยนแปลง
                db.commit()
