import pandas as pd
import numpy as np
import decimal
from sqlalchemy import create_engine, text

# สร้างการเชื่อมต่อฐานข้อมูลด้วย SQLAlchemy
# engine = create_engine('mssql+pyodbc://your_username:your_password@your_server/your_database?driver=SQL+Server')
engine = create_engine('mssql+pyodbc://powerbi:Nation#2021@************/DW?driver=SQL+Server')

# ดึงข้อมูลจากฐานข้อมูลโดยกรอง update_date ตั้งแต่ปี 2020 เป็นต้นไป
query = """
SELECT employee_id, salary_pm, travel_pm, telephone_pm, position_fee_pm, internet_pm,
       special_fee_pm, host_fee_pm, total_income_pm, salary, travel, telephone, 
       position_fee, internet, special_fee, host_fee, total_income, pf_comp, 
       pf_emp, social, update_date
FROM employee_profile
WHERE update_date >= '2020-01-01'
"""

# ดึงข้อมูลมาใส่ใน DataFrame
with engine.connect() as conn:
    df = pd.read_sql(query, conn)

# ตรวจสอบประเภทข้อมูลและจัดการกับ NaN/None
df.fillna(0, inplace=True)  # เติมค่า NaN ด้วย 0

# แปลงค่าที่เป็น float ให้เป็น decimal (ถ้าจำเป็น)
def convert_to_decimal(x):
    return decimal.Decimal(x) if isinstance(x, float) else x

df = df.applymap(convert_to_decimal)

# ฟังก์ชันการสุ่มสลับข้อมูลในแนว row หลายครั้งโดยใช้ numpy
def shuffle_data_within_group(group, num_shuffles=3):
    # คัดลอกข้อมูลเพื่อไม่ให้กระทบข้อมูลต้นฉบับ
    group = group.copy()
    
    # เก็บ employee_id และ update_date ไว้
    employee_ids = group['employee_id'].values
    update_dates = group['update_date'].values
    
    # ลบคอลัมน์ที่ไม่ต้องการสลับ
    columns_to_shuffle = group.columns.drop(['employee_id', 'update_date'])
    
    # สลับข้อมูลในคอลัมน์ที่เหลือหลายครั้งตามที่กำหนด
    shuffled_data = group[columns_to_shuffle].values
    for _ in range(num_shuffles):
        np.random.shuffle(shuffled_data)
        
    # แบ่งค่าทั้งหมดในคอลัมน์ที่ต้องการด้วย 10
    shuffled_data = shuffled_data / 10
    
    # สร้าง DataFrame ใหม่จากข้อมูลที่สลับแล้ว
    shuffled_group = pd.DataFrame(shuffled_data, columns=columns_to_shuffle)
    
    # เพิ่มคอลัมน์ employee_id และ update_date กลับเข้าไป
    shuffled_group['employee_id'] = employee_ids
    shuffled_group['update_date'] = update_dates  
    
    return shuffled_group


# กำหนดจำนวนครั้งในการสลับข้อมูล
num_shuffles = 3  # สลับข้อมูล 3 ครั้ง

# การจัดการการสลับในกลุ่มตาม update_date โดยแยกคอลัมน์กลุ่มออก
df_shuffled = df.groupby('update_date', group_keys=False).apply(lambda x: shuffle_data_within_group(x, num_shuffles))
# อัปเดตข้อมูลกลับไปยังฐานข้อมูล
update_query_template = text("""
UPDATE employee_profile
SET salary_pm = :salary_pm, travel_pm = :travel_pm, telephone_pm = :telephone_pm, 
    position_fee_pm = :position_fee_pm, internet_pm = :internet_pm, 
    special_fee_pm = :special_fee_pm, host_fee_pm = :host_fee_pm, 
    total_income_pm = :total_income_pm, salary = :salary, travel = :travel, 
    telephone = :telephone, position_fee = :position_fee, internet = :internet, 
    special_fee = :special_fee, host_fee = :host_fee, total_income = :total_income, 
    pf_comp = :pf_comp, pf_emp = :pf_emp, social = :social
WHERE employee_id = :employee_id AND update_date = :update_date
""")

# เตรียมค่าเพื่อทำการอัปเดต
update_data = [
    {
        'salary_pm': row['salary_pm'], 'travel_pm': row['travel_pm'], 'telephone_pm': row['telephone_pm'],
        'position_fee_pm': row['position_fee_pm'], 'internet_pm': row['internet_pm'],
        'special_fee_pm': row['special_fee_pm'], 'host_fee_pm': row['host_fee_pm'],
        'total_income_pm': row['total_income_pm'], 'salary': row['salary'], 'travel': row['travel'],
        'telephone': row['telephone'], 'position_fee': row['position_fee'],
        'internet': row['internet'], 'special_fee': row['special_fee'], 'host_fee': row['host_fee'],
        'total_income': row['total_income'], 'pf_comp': row['pf_comp'], 'pf_emp': row['pf_emp'],
        'social': row['social'], 'employee_id': row['employee_id'], 'update_date': row['update_date']
    }
    for _, row in df_shuffled.iterrows()
]

# อัปเดตข้อมูลกลับไปยังฐานข้อมูล
with engine.connect() as conn:
    with conn.begin() as transaction:  # ใช้ transaction เพื่อให้การอัปเดตปลอดภัย
        conn.execute(update_query_template, update_data)
