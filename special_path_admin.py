import sys
import pyodbc
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QListWidget, QWidget, QVBoxLayout, QPushButton, QHBoxLayout
from PyQt5.QtWidgets import QTextEdit, QLineEdit

# สมมติว่าคุณมี connection string สำหรับ MS SQL Server ของคุณ
conn_str = (
    r'DRIVER={ODBC Driver 17 for SQL Server};'
    r'SERVER=172.16.1.172;'
    r'DATABASE=workflow;'
    r'UID=sys_wkf;'
    r'PWD=Nation#2021'
)

# สร้างการเชื่อมต่อ
conn = pyodbc.connect(conn_str)
cursor = conn.cursor()

# ดำเนินการ query
cursor.execute("SELECT employee_id, fullname_th FROM user_profile WHERE status = 1")

# สร้าง dictionary สำหรับเก็บคู่ fullname_th และ employee_id
user_data = {}

for row in cursor:
    # เพิ่มข้อมูลลงใน dictionary
    user_data[row.fullname_th] = row.employee_id

cursor.close()
conn.close()

class MyApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.sourceListWidget.itemDoubleClicked.connect(self.moveToResult)

    def initUI(self):
        self.layout = QVBoxLayout()

        # Search
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search...")
        self.search_btn = QPushButton("Search")
        self.search_btn.clicked.connect(self.searchItems)
        self.search_btn.setDefault(True)
        self.search_btn.setShortcut("Return")
        self.search_layout = QHBoxLayout()
        self.search_layout.addWidget(self.search_input)
        self.search_layout.addWidget(self.search_btn)
        self.layout.addLayout(self.search_layout)

        self.sourceListWidget = QListWidget()
        self.resultListWidget = QListWidget()

        # ใส่ข้อมูล fullname_th ลงใน sourceListWidget
        for fullname_th in user_data.keys():
            self.sourceListWidget.addItem(fullname_th)

        # Buttons for moving items
        self.btn_move_to_result = QPushButton("Move to Result >>")
        self.btn_move_to_result.clicked.connect(lambda: self.moveItems(self.sourceListWidget, self.resultListWidget))

        self.btn_move_to_source = QPushButton("<< Move to Source")
        self.btn_move_to_source.clicked.connect(lambda: self.moveItems(self.resultListWidget, self.sourceListWidget))

        self.btn_process = QPushButton("Process")        
        self.btn_process.clicked.connect(self.processItems)

        self.btn_clear = QPushButton("Clear")
        self.btn_clear.clicked.connect(self.clearResultList)

        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)

        # Button layout
        self.btn_layout = QHBoxLayout()
        self.btn_layout.addWidget(self.btn_move_to_source)
        self.btn_layout.addWidget(self.btn_move_to_result)
        self.btn_layout.addWidget(self.btn_process)
        self.btn_layout.addWidget(self.btn_clear)

        self.layout.addWidget(self.sourceListWidget)
        self.layout.addLayout(self.btn_layout)
        self.layout.addWidget(self.resultListWidget)
        self.layout.addWidget(self.result_text)

        self.setLayout(self.layout)
        self.setWindowTitle('User Profiles')
        self.setGeometry(300, 300, 400, 300)

    def searchItems(self):
        search_text = self.search_input.text().lower()
        self.sourceListWidget.clear()
        for fullname_th in user_data.keys():
            if search_text in fullname_th.lower():
                self.sourceListWidget.addItem(fullname_th)

    def clearResultList(self):
        self.resultListWidget.clear()
        self.result_text.clear()

    def moveItems(self, source, destination):
        for selectedItem in source.selectedItems():
            destination.addItem(selectedItem.text())
            source.takeItem(source.row(selectedItem))

    def moveToResult(self, item):
        self.resultListWidget.addItem(item.text())
        self.sourceListWidget.takeItem(self.sourceListWidget.row(item))

    def processItems(self):
        result_str = ""
        for index in range(self.resultListWidget.count()):
            fullname_th = self.resultListWidget.item(index).text()
            employee_id = user_data.get(fullname_th, "")
            result_str += f'"{employee_id}",'
        result_str = result_str.rstrip(",")
        self.result_text.setText(result_str)         

def main():
    app = QApplication(sys.argv)
    ex = MyApp()
    ex.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
