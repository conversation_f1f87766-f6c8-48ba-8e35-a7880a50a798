import pandas as pd
import pyodbc

# ตั้งค่าเชื่อมต่อฐานข้อมูล MSSQL
conn_str = (
    "DRIVER={SQL Server};"
    "SERVER=************;"
    "DATABASE=EP;"
    "UID=panakngan;"
    "PWD=Employee#2024;"
)
conn = pyodbc.connect(conn_str)
cursor = conn.cursor()

# โหลดข้อมูลจากไฟล์ Excel โดยเริ่มที่บรรทัดที่ 7
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\1_Emp January 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\2_Emp February 2566.xlsx', skiprows=5, engine='openpyxl')
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\3_Emp March 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\4_Emp April 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\5_Emp May 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\6_Emp June 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\7_Emp July 2556.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\8_Emp August 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\9_Emp September 2566.xlsx', skiprows=5)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\10_Emp October 2566.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\11_Emp November 2566.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2023\\12_Emp December 2566.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2024\\1_Emp January 2024.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2024\\2_Emp February 2567.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2024\\3_Emp March 2567.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2024\\4_Emp April 2567.xlsx', skiprows=2)
# df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2024\\5_Emp May 2567.xlsx', skiprows=2)
df = pd.read_excel('C:\\FileHistory\\WorkSpace\\Working_Temp\\PM PY Data\\Excel_File\\2024\\6_Emp June 2567.xlsx', skiprows=2)

# print(df.head(1))

debug_flag = True
update_date = '2024-06-01'

# ฟังก์ชันสำหรับตรวจสอบและอัปเดตข้อมูลในฐานข้อมูล
def update_employee_profile(row):
    # 9 is J
    emp_id = row.iloc[9] 
    print('Read: ' + str(emp_id))
    if pd.notna(emp_id) and isinstance(emp_id, int) and len(str(emp_id)) == 6:
        update_values = {}
        # 19 is T, 23 is X
        start_col = 19
        if debug_flag:
            print('Salary: ' + str(row.iloc[start_col]) + ' Total: ' + str(row.iloc[start_col + 7]))
        if pd.notna(row.iloc[start_col]):
            # update_values['comp_name'] = row.iloc[0].strip()
            # update_values['division_name'] = row.iloc[1].strip()
            # update_values['department_name'] = row.iloc[2].strip()
            # update_values['section_name'] = row.iloc[3].strip()
            # bu_code = str(row.iloc[4]) + str(row.iloc[5]) + str(row.iloc[6]) + str(row.iloc[7])
            # if len(bu_code) == 12:
            #     update_values['bu_code'] = bu_code
            update_values['salary_pm'] = float(row.iloc[start_col])
            update_values['travel_pm'] = float(row.iloc[start_col + 1]) if pd.notna(row.iloc[start_col + 1]) else 0.0        
            update_values['telephone_pm'] = float(row.iloc[start_col + 2]) if pd.notna(row.iloc[start_col + 2]) else 0.0        
            update_values['position_fee_pm'] = float(row.iloc[start_col + 3]) if pd.notna(row.iloc[start_col + 3]) else 0.0        
            update_values['internet_pm'] = float(row.iloc[start_col + 4]) if pd.notna(row.iloc[start_col + 4]) else 0.0        
            update_values['host_fee_pm'] = float(row.iloc[start_col + 5]) if pd.notna(row.iloc[start_col + 5]) else 0.0        
            update_values['special_fee_pm'] = float(row.iloc[start_col + 6]) if pd.notna(row.iloc[start_col + 6]) else 0.0        
            update_values['total_income_pm'] = float(row.iloc[start_col + 7]) if pd.notna(row.iloc[start_col + 7]) else 0.0
        
        if float(row.iloc[start_col+7]) > 0.0:
            if update_values:
                # update_query = "UPDATE employee_profile SET "
                # for field, value in update_values.items():
                #     update_query += f", {field} = ?"
                # update_query += " WHERE employee_id = ? and update_date = '2023-01-01'"

                update_query = "UPDATE employee_profile SET "
                update_query += ", ".join([f"{field} = ?" for field in update_values.keys()])
                update_query += " WHERE employee_id = ? and update_date = '"+ update_date +"'"                
                    
                print('Updated: ' + str(emp_id) + ', Update: ' + update_date + ', debug_flag: ' + str(debug_flag))
                if not debug_flag:
                    cursor.execute(update_query, *update_values.values(), emp_id)
                    conn.commit()
                else:
                    print (update_query)
        else:
            print('Skip Update: ' + str(emp_id) + ', debug_flag: ' + str(debug_flag))

# วนลูปตรวจสอบและอัปเดตข้อมูลตามเงื่อนไขที่กำหนด
df.apply(update_employee_profile, axis=1)

if debug_flag:
    print(df.iloc[0])

# ปิดการเชื่อมต่อฐานข้อมูล
cursor.close()
conn.close()
