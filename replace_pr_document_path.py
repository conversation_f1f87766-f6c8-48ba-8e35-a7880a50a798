import os
import json
import pyodbc
from dotenv import load_dotenv

def load_config(env_file: str):
    """โหลดตัวแปรสภาพแวดล้อมจากไฟล์ .env"""
    load_dotenv(dotenv_path=env_file)


def get_connection_string() -> str:
    """ประกอบสตริงเชื่อมต่อ SQL Server จาก env vars"""
    server   = os.getenv('DB_SERVER')
    port     = int(os.getenv('DB_PORT', 1433))
    database = os.getenv('DB_DATABASE')
    user     = os.getenv('DB_USER')
    pwd      = os.getenv('DB_PASSWORD')
    return (
        f'DRIVER={{ODBC Driver 17 for SQL Server}};'
        f'SERVER=tcp:{server},{port};'
        f'DATABASE={database};UID={user};PWD=*****'
    )


def fetch_special_config(cursor, special_name: str) -> dict:
    """
    ดึง json_data และ credit_limit สำหรับ special_path.name ที่กำหนด
    Returns:
        dict: มี key 'json_data' (เป็น dict ที่ parse แล้ว) และ 'credit_limit'
              หรือ dict ว่าง {'json_data': {}, 'credit_limit': None} ถ้าไม่พบข้อมูลหรือมีข้อผิดพลาด
    """
    try:
        # --- เปลี่ยนแปลง SQL ---
        cursor.execute(
            "SELECT json_data, special_limit FROM special_path WHERE name = ?",
            (special_name,)
        )
        row = cursor.fetchone()

        if row:
            parsed_json = {}
            special_limit_value = None

            # --- เปลี่ยนแปลงการประมวลผล ---
            # ดึงค่า credit_limit (อาจจะเป็น None)
            special_limit_value = row.special_limit

            # พยายาม parse json_data ถ้ามีข้อมูล
            if row.json_data:
                try:
                    parsed_json = json.loads(row.json_data)
                except json.JSONDecodeError:
                    print(f"[ERROR] fetch_special_config: Invalid JSON data for special_path name: {special_name}")
                    # กรณี JSON ไม่ถูกต้อง อาจจะคืนค่า default หรือจัดการตามที่ต้องการ
                    # ที่นี่เราจะคืน JSON ว่าง แต่ยังคง special_limit ไว้
                    parsed_json = {}

            # คืนค่าเป็น dictionary ที่มีทั้งสองค่า
            return {'json_data': parsed_json, 'special_limit': special_limit_value}
        else:
            # ไม่พบข้อมูล คืนค่า default
            print(f"[WARN] fetch_special_config: No data found for special_path name: {special_name}")
            return {'json_data': {}, 'special_limit': None}

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"[ERROR] fetch_special_config: Database error for {special_name}. SQLSTATE: {sqlstate}. Error: {ex}")
        return {'json_data': {}, 'special_limit': None}
    except Exception as e:
        print(f"[ERROR] fetch_special_config: Unexpected error for {special_name}: {e}")
        return {'json_data': {}, 'special_limit': None}


def enrich_member(cursor, comp_no: str, emp_id: str) -> dict:
    """
    ดึงข้อมูล employee_name, employee_level จาก employee_profile
    และ credit_limit จาก wf_loa ตาม comp_no และระดับ
    """
    cursor.execute(
        "SELECT fullname_th, wf_level FROM user_profile WHERE employee_id = ?",
        (emp_id,)
    )
    row_ep = cursor.fetchone()
    name, level = (row_ep.fullname_th, str(row_ep.wf_level)) if row_ep else ("", "")

    cursor.execute(
        """
        SELECT credit_limit
        FROM wf_loa
        WHERE comp_no    = ?
          AND loa_type   = 'RM'
          AND [level]    = ?
        """, (comp_no, level)
    )
    row_loa = cursor.fetchone()
    credit = int(row_loa.credit_limit) if row_loa and row_loa.credit_limit is not None else 0

    return {
        "employee_id":    emp_id,
        "employee_name":  name,
        "employee_level": level,
        "credit_limit":   str(credit)
    }


def update_pr_loa_members(conn, debug: bool = True):
    """
    อัปเดต LOA role_member ใน pr_header.document_path
    - โหลด special_path
    - เติมข้อมูลเต็มจาก employee_profile + wf_loa (ข้ามสมาชิกคนแรก)
    - เมื่อ debug=True จะแสดง pretty JSON
    """
    cur = conn.cursor()
    cur.execute(
        """
        SELECT pr_id, pr_comp, special_path, document_path
        FROM pr_header
        WHERE special_path IS NOT NULL AND special_path <> ''
        and not status_code in ('PR_CANCELLED','PR_DISAPPROVE')
        -- and pr_id = 2539
        and pr_id in (select pr_id from po_header where po_comp = '00007' and po_no = 680005)
        """
    )
    rows = cur.fetchall()

    for pr_id, comp_no, special_name, doc_json in rows:
        # แสดง raw JSON
        if debug:
            try:
                raw_obj = json.loads(doc_json)
                pretty_raw = json.dumps(raw_obj, ensure_ascii=False, indent=2)
            except Exception:
                pretty_raw = doc_json
            print(f"[DEBUG] PR {pr_id}: special_path: {special_name}")
            # print(f"[DEBUG] PR {pr_id}: raw document_path JSON:\n{pretty_raw}\n")

        # โหลด document_path
        try:
            doc_list = json.loads(doc_json)
        except json.JSONDecodeError:
            print(f"[ERROR] PR {pr_id}: invalid JSON in document_path")
            continue

        # โหลด special_path config
        sp_conf = fetch_special_config(cur, special_name)
        json_data_dict = sp_conf.get('json_data', {}) # ดึง dict ของ json_data ออกมาก่อน
        special_limit = sp_conf.get('special_limit', 0);

        # --- ตรวจสอบและแปลง special_limit ---
        special_limit_val = 0
        if special_limit is not None:
            try:
                # ลองแปลงเป็น float ก่อนเผื่อเป็นทศนิยม แล้วค่อยเป็น int
                special_limit_val = int(float(special_limit))
            except (ValueError, TypeError):
                print(f"[WARN] PR {pr_id}: Invalid special_limit value '{special_limit}'. Using 0.")
                special_limit_val = 0
        # ------------------------------------
        raw_members = json_data_dict.get('approve_path', [])
        if len(raw_members) < 2:
            print(f"[WARN] PR {pr_id}: not enough special_path members '{special_name}'")
            continue
        elif DEBUG == True:
            print(f"[DEBUG] special_path members '{raw_members}', special_limit: {special_limit}")

        # # ข้ามสมาชิกคนแรก และ enrich รายละเอียด
        # members_to_use = raw_members[1:]
        # enriched = []
        # for m in members_to_use:
        #     emp_id = m.get('employee_id')
        #     if emp_id:
        #         enriched.append(enrich_member(cur, comp_no, emp_id))

        # --- แก้ไขส่วนนี้: วนลูปพร้อม index เพื่อเช็คคนสุดท้าย ---
        members_to_use = raw_members[1:] # ข้ามสมาชิกคนแรกเหมือนเดิม
        num_members_to_use = len(members_to_use)
        enriched = []
        for index, m in enumerate(members_to_use):
            emp_id = m.get('employee_id')
            if emp_id:
                # 1. เรียก enrich_member เพื่อดึงข้อมูลพื้นฐาน (รวม credit_limit ปกติ)
                member_data = enrich_member(cur, comp_no, emp_id)

                # 2. ตรวจสอบว่าเป็นสมาชิกคนสุดท้ายหรือไม่ และ special_limit > 0
                is_last_member = (index == num_members_to_use - 1)
                if is_last_member and special_limit_val > 0:
                    original_limit = member_data.get('credit_limit', 'N/A')
                    if debug:
                        print(f"[DEBUG] PR {pr_id}: Overriding credit limit for last member {emp_id} "
                              f"(Original: {original_limit}) with special_limit: {special_limit_val}")
                    # 3. Override ค่า credit_limit ด้วย special_limit (แปลงเป็น string)
                    member_data['credit_limit'] = str(special_limit_val)

                # 4. เพิ่มข้อมูล (ที่อาจถูก override แล้ว) ลงใน list
                enriched.append(member_data)
            else:
                 print(f"[WARN] PR {pr_id}: Member found without employee_id in special_path: {m}")
        # --- สิ้นสุดการแก้ไข ---

        # อัปเดตทุกขั้นตอนที่ role_response == 'LOA'
        changed = False
        for step in doc_list:
            if step.get('role_response') == "LOA":
                if debug:
                    exist = json.dumps(step.get('role_member', []), ensure_ascii=False, indent=2)
                    newm  = json.dumps(enriched, ensure_ascii=False, indent=2)
                    print(f"[DEBUG] PR {pr_id}: existing LOA members:\n{exist}\n")
                    print(f"[DEBUG] PR {pr_id}: replacing with:\n{newm}\n")
                step['role_member'] = enriched
                changed = True

        # คอมมิตหรือแสดงผล
        if changed:
            if debug:
                pretty_doc = json.dumps(doc_list, ensure_ascii=False, indent=2)
                print(f"[DEBUG] PR {pr_id}: document_path after replace:\n{pretty_doc}\n")
            else:
                cur.execute(
                    "UPDATE pr_header SET document_path = ? WHERE pr_id = ?",
                    (json.dumps(doc_list, ensure_ascii=False), pr_id)
                )
                conn.commit()
            print(f"[OK] PR {pr_id}: LOA members updated ({len(enriched)} entries)")
        else:
            print(f"[SKIP] PR {pr_id}: no LOA step to update")

    cur.close()


if __name__ == "__main__":
    # ENV_FILE = "aqua_dev.env"
    ENV_FILE = "aqua_prod.env"
    DEBUG    = False  # เปลี่ยนเป็น False เมื่อพร้อมเขียนจริง

    load_config(ENV_FILE)
    conn = pyodbc.connect(get_connection_string())
    print("Start updating PR.document_path (LOA)...")
    update_pr_loa_members(conn, debug=DEBUG)
    conn.close()
    print("Done.")
