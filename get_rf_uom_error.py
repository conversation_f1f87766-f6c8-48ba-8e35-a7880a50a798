import pyodbc 
import json
import pandas as pd
import re

# Nation Server
# server = '172.16.1.172' 
# port = 1433
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Nation#2021' 

# Aqua Server Dev
# server = '52.76.139.120' 
# port = 1533
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Aqua#2022' 

# Aqua Server Production
server = '52.76.139.120' 
port = 1433
database = 'workflow' 
username = 'sys_wkf' 
password = 'Aqua#2022' 

connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

conn = pyodbc.connect(connection_string)

# ข้อความตัวอย่างที่มีข้อมูล DocumentNumber และ Material code
# data = """
# DocumentNumber (007-RF-660054) : Material code = D01020 : UnitName for this material is not found in database.
# DocumentNumber (007-RF-660030) : Material code = F02001 : UnitName for this material is not found in database.
# """

data = """
DocumentNumber (007-RF-660082) : Material code = N03004 : UnitName for this material is not found in database.
DocumentNumber (007-RF-660077) : Material code = F02031 : UnitName for this material is not found in database.
DocumentNumber (007-RF-660063) : Material code = Y01001 : UnitName for this material is not found in database.
"""

# ใช้ regular expression เพื่อค้นหาคู่ของ DocumentNumber และ Material code
pattern = r"DocumentNumber \((.*?)\) : Material code = (.*?) :"

# ค้นหาทุกคู่ที่ตรงกับ pattern
matches = re.findall(pattern, data)

sql = "select uom, rf_id from rf_detail where rf_id in (select rf_id from rf_header where rf_comp = ? and rf_no=?) and item_code= ? "
# แสดงผลลัพธ์
print ("UnitName for this material is not found in database.")
list_rf = ""
for match in matches:
    parts = match[0].split('-')    
    params = ('00'+ parts[0], parts[2], match[1])    
    cursor = conn.cursor()
    cursor.execute(sql, params)

    rows = cursor.fetchall()
    for row in rows:    
        print(f"DocumentNumber: {match[0]}, Material code: {match[1]}, Unit name: {row[0]}")
        list_rf = list_rf + ',' + str(row[1])

    cursor.close()
print ('List of RF_ID:', list_rf)
