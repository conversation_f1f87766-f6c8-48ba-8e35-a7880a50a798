import pyodbc 
import json
import pandas as pd

# Nation Server
# server = '172.16.1.172' 
# port = 1433
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Nation#2021' 

# Aqua Server Dev
# server = '52.76.139.120' 
# port = 1533
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Aqua#2022' 

# Aqua Server Production
server = '52.76.139.120' 
port = 1433
database = 'workflow' 
username = 'sys_wkf' 
password = 'Aqua#2022' 

show_credit_limit = False

connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

conn = pyodbc.connect(connection_string)

cursor = conn.cursor()
sql = """
    select * from wf_roles_member where comp_no = '00007' -- and json_data like '%650001%'
"""
cursor.execute(sql)
rows = cursor.fetchall()
print('Begin Read Role Member')

# create file
writer = pd.ExcelWriter('roles_member.xlsx', engine='xlsxwriter')
col1=[]
col2=[]
col3=[]
col4=[]
columns=['Company','Role_Code','Role_Desc','Member']

for row in rows: 
    name = row.role_code
    comp_no = row.comp_no
    desc = row.role_desc    
    
    # print(name, ' ',row.json_data)
    json_data = json.loads(row.member)
    simple_path = ""
    first_flag = True
    # print(json_data)
    for approve_path in json_data['member_list']:
            employee_id = approve_path['employee_id']
            credit_limit = 0
            str_level = ''
            # if employee_id:
            #     # Get Level
            #     emp_cursor = conn.cursor()
            #     sql = """                    
            #         select wf_level, hr_level from user_profile where employee_id = ?
            #     """
            #     params = (employee_id)
            #     emp_cursor.execute(sql, params)
            #     emp_row = emp_cursor.fetchone()
            #     if emp_row:    
            #         if emp_row.hr_level == emp_row.wf_level:                 
            #             str_level = 'Lv: ' + str(emp_row.wf_level)
            #         else:
            #             str_level = 'Lv: ' + str(emp_row.hr_level) + '-' + str(emp_row.wf_level)
            #     emp_cursor.close()
            #     # Get Credit Limit
            #     emp_cursor = conn.cursor()
            #     sql = """
            #         select credit_limit from wf_loa where comp_no = ? and loa_type = 'RM' 
            #         and [level] = (select wf_level from user_profile where employee_id = ?)
            #     """
            #     params = (comp_no, employee_id)
            #     emp_cursor.execute(sql, params)
            #     emp_row = emp_cursor.fetchone()
            #     if emp_row:
            #         credit_limit = emp_row.credit_limit                     
            #     emp_cursor.close()
            emp_info = approve_path['employee_name']
            # if show_credit_limit and credit_limit > 0 and not first_flag:
            #     emp_info = approve_path['employee_name'] + ' (' +str_level + ' Cr: ' + f'{credit_limit:,}' + ')'
            if first_flag:
                simple_path = simple_path + emp_info
                first_flag = False
            else:
                simple_path = simple_path + ", " + emp_info
    print ( " Comp: ", comp_no, "Name: ",name, " Approver: ", simple_path)    
    col1.append(comp_no)
    col2.append(name)
    col3.append(desc)
    col4.append(simple_path)
    # col4.append(special_limit)
        
df = pd.DataFrame(list(zip(col1,col2,col3,col4,)), columns=columns)
df.to_excel(writer, sheet_name='Sheet1', index=False)

writer.close()
conn.close()
print('Success Create: roles_member.xlsx')


