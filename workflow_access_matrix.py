import pandas as pd
import pyodbc
from openpyxl import Workbook
from dotenv import load_dotenv
import os

# โหลดค่าจากไฟล์ aqua_prod.env (ระบุชื่อไฟล์)
load_dotenv(dotenv_path="aqua_prod.env")

# ดึงค่าจากไฟล์ aqua_prod.env
db_driver = os.getenv("DB_DRIVER")
db_server = os.getenv("DB_SERVER")
db_database = os.getenv("DB_DATABASE")
db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")

# ตรวจสอบว่าค่าใน aqua_prod.env ถูกต้องครบถ้วน
if not all([db_driver, db_server, db_database, db_user, db_password]):
    raise EnvironmentError("Missing database credentials in aqua_prod.env file.")

# ตั้งค่าการเชื่อมต่อกับ MSSQL
conn_str = f"DRIVER={db_driver};SERVER={db_server};DATABASE={db_database};UID={db_user};PWD={db_password}"
conn = pyodbc.connect(conn_str)

# ดึงข้อมูลจาก user_profile
query_users = """
SELECT employee_id, fullname_th, comp_code, bu_name, position
FROM user_profile
WHERE status = 1
"""
df_users = pd.read_sql(query_users, conn)

# ดึงข้อมูล role ของแต่ละ user
query_roles = """
SELECT employee_id, role_code
FROM user_role
"""
df_roles = pd.read_sql(query_roles, conn)

# ดึงข้อมูล Accounting Role (ไม่ต้อง DISTINCT แล้ว)
query_accounting_roles = """
SELECT member
FROM wf_roles_member
WHERE role_code IN ('Account_Admin','Account_Head')
"""
df_accounting_roles = pd.read_sql(query_accounting_roles, conn)

# ปิดการเชื่อมต่อ
conn.close()

# แปลงข้อมูล role เป็น dict {employee_id: [role1, role2, ...]}
role_dict = df_roles.groupby('employee_id')['role_code'].apply(list).to_dict()

# สร้าง list ของ accounting_members (เก็บค่า member ทั้งหมด)
accounting_members = df_accounting_roles['member'].tolist()

# ฟังก์ชันช่วยตรวจสอบสิทธิ (ปรับปรุงใหม่)
def get_role_access(employee_id):
    roles = role_dict.get(employee_id, [])
    roles_str = ", ".join(roles)  # รวม roles เป็น string คั่นด้วย comma
    admin_access = '✔' if 'admin' in roles else ''
    monitor_access = '✔' if 'monitor' in roles else ''
    # ตรวจสอบ Accounting Role แบบ LIKE
    accounting_role = '✔' if any(employee_id in member for member in accounting_members) else ''
    return pd.Series({'Roles': roles_str, 'Admin_Access': admin_access, 'Monitor_Access': monitor_access, 'Accounting_Role': accounting_role})

# เพิ่มข้อมูล role เข้าไปใน df_users (ปรับปรุงใหม่)
role_data = df_users['employee_id'].apply(get_role_access)
df_final = pd.concat([df_users, role_data], axis=1)

# บันทึกเป็น Excel
output_file = "Workflow_Access_Matrix.xlsx"
df_final.to_excel(output_file, index=False)

print(f"ไฟล์ {output_file} ถูกสร้างเรียบร้อยแล้ว")
