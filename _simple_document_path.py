import pyodbc 
import json
import pandas as pd
from dotenv import load_dotenv
import os

# Load environment variables from .env file
# Aqua Server Production
load_dotenv('aqua_prod.env') 
# Nation Server
# load_dotenv('nation_prod.env') 
# FAB Server Production
# load_dotenv('fab_prod.env') 

# Access environment variables
server = os.getenv('DB_SERVER')
port = int(os.getenv('DB_PORT'))  # Convert port to integer
database = os.getenv('DB_DATABASE')
username = os.getenv('DB_USER')
password = os.getenv('DB_PASSWORD')


show_credit_limit = True
show_user_name = False

# ถ้า pr_path = True จะแสดงสายของ PR ถ้าเป็น False จะแสดงสายของ PV
path_type = 'GR' #PV, PR, OF, GR

connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

conn = pyodbc.connect(connection_string)

cursor = conn.cursor()
sql = """
    -- select * from special_path where not comp_no like '.%'  -- comp_no in ('00008','00009','00010','00011','00012','00013') -- and json_data like '%650001%'
    -- select * from special_path where comp_no in ('00015','00016')
    select * from special_path where not comp_no like '.%' and comp_no in (select comp_no from company where status = 1) and not name like '%-TEST'
"""
cursor.execute(sql)
rows = cursor.fetchall()
print('Begin Read Special Path')

# create file
writer = pd.ExcelWriter('document_path.xlsx', engine='xlsxwriter')
col1=[]
col2=[]
col3=[]
col4=[]
col5=[]
col6=[]
col7=[]
col8=[]
col9=[]
col10=[]
col11=[]

columns_map = {
    'PR': ['Name', 'Company', 'ผู้บังคับบัญชาอนุมัติ', 'บัญชีตรวจสอบเอกสาร', 'หัวหน้าบัญชี อนุมัติ', 'ผู้มีอำนาจ อนุมัติ', 'ผู้อำนวยการ ฝ่ายบัญชี อนุมัติ', 'แผนกจัดซื้อตรวจสอบ', 'ผู้จัดการฝ่ายจัดซื้อ อนุมัติ', 'ผู้อำนวยการ ฝ่ายจัดซื้อ อนุมัติ', 'Special_limit'],
    'OF': ['Name', 'Company', 'จัดซื้อ ตรวจสอบ', 'หัวหน้าฝ่ายจัดซื้อ ตรวจสอบ', 'ผู้จัดการทั่วไป อนุมัติ', 'ผู้จัดการฝ่ายจัดซื้อ อนุมัติ', 'Special_limit'],
    'GR': ['Name', 'Company', 'บัญชี แก้ไข VAT / TAX', 'การเงินตรวจสอบเอกสาร', 'ผู้บังคับบัญชา อนุมัติ', 'Special_limit'],
}

default_columns = ['Name', 'Company', 'ผู้บังคับบัญชาอนุมัติ', 'บัญชีตรวจสอบเอกสาร', 'หัวหน้าบัญชี อนุมัติ', 'ผู้มีอำนาจ อนุมัติ', 'ผู้อำนวยการ ฝ่ายบัญชี อนุมัติ', 'บัญชีตรวจสอบเอกสาร ก่อนตั้งหนี้', 'Special_limit']

columns = columns_map.get(path_type, default_columns)

for row in rows: 
    name = row.name
    comp_no = row.comp_no  
    # special_limit = row.special_limit 
    if row.special_limit is not None:
        try:
            special_limit = int(row.special_limit)
            special_limit = f'{special_limit:,}'
        except ValueError: 
            special_limit = ""
    else:
        special_limit = ""
    if comp_no[0] != '.' or True:
        # print(name, ' ',row.json_data)
        json_data = json.loads(row.json_data)
        cmd_review = ""
        aup_approve = ""
        first_flag = True
        # print(json_data)
        for approve_path in json_data['approve_path']:
                employee_id = approve_path['employee_id']
                credit_limit = 0
                str_level = ''
                str_username = ''
                if employee_id:
                    # Get Level
                    emp_cursor = conn.cursor()
                    sql = """                    
                        select wf_level, hr_level, username from user_profile where employee_id = ?
                    """
                    params = (employee_id)
                    emp_cursor.execute(sql, params)
                    emp_row = emp_cursor.fetchone()
                    if emp_row: 
                        str_username = emp_row.username    
                        if emp_row.hr_level == emp_row.wf_level:                 
                            str_level = 'Lv: ' + str(emp_row.wf_level)
                        else:
                            str_level = 'Lv: ' + str(emp_row.hr_level) + '-' + str(emp_row.wf_level)
                    emp_cursor.close()
                    # Get Credit Limit
                    emp_cursor = conn.cursor()
                    sql = """
                        select credit_limit from wf_loa where comp_no = ? and loa_type = 'RM' 
                        and [level] = (select wf_level from user_profile where employee_id = ?)
                    """
                    params = (comp_no, employee_id)
                    emp_cursor.execute(sql, params)
                    emp_row = emp_cursor.fetchone()
                    if emp_row:
                        credit_limit = emp_row.credit_limit                     
                    emp_cursor.close()
                emp_info = approve_path['employee_name']
                # if show_credit_limit and credit_limit > 0 and not first_flag:
                if show_credit_limit and credit_limit > 0:
                    emp_info = approve_path['employee_name'] + ' (' +str_level + ' Cr: ' + f'{credit_limit:,}' + ')'
                if show_user_name:
                    emp_info = emp_info + ' (' + str_username + ')'
                if first_flag:
                    cmd_review = aup_approve + emp_info
                    if credit_limit > 0:
                        aup_approve = aup_approve + emp_info
                    first_flag = False
                else:
                    if aup_approve:
                        aup_approve = aup_approve + " => " + emp_info
                    else:
                        aup_approve = aup_approve + emp_info


        def get_members(role_code, comp_no):
            sql = f"SELECT member FROM wf_roles_member WHERE role_code = '{role_code}' AND comp_no = '{comp_no}'"
            cursor.execute(sql)
            row = cursor.fetchone()
            
            if row:
                members_data = json.loads(row.member)
                members_list = []
                for member in members_data['member_list']:
                    employee_id = member['employee_id']
                    employee_name = member['employee_name']
                    credit_limit = member.get('credit_limit')
                    str_username = ''
                    if show_user_name:
                        if employee_id:
                            # Get Level
                            emp_cursor = conn.cursor()
                            sql = """                    
                                select username from user_profile where employee_id = ?
                            """
                            params = (employee_id)
                            emp_cursor.execute(sql, params)
                            emp_row = emp_cursor.fetchone()
                            if emp_row: 
                                str_username = emp_row.username    
                            emp_cursor.close()                        
                    if credit_limit is not None:
                        try:
                            # แปลง credit_limit เป็น int หากสามารถทำได้
                            credit_limit = int(credit_limit)
                            members_list.append(f"{employee_name} (Cr: {credit_limit:,})")
                        except ValueError:
                            # กรณีที่ credit_limit ไม่สามารถแปลงเป็น int ได้ ให้แสดงเฉพาะชื่อ
                            if show_user_name:                            
                                members_list.append(f"{employee_name} ({str_username})")
                            else:
                                members_list.append(employee_name)
                    else:
                        if show_user_name:
                            members_list.append(f"{employee_name} ({str_username})")
                        else:
                            members_list.append(employee_name)
                
                return ', '.join(members_list)
            return ""
        
        acc_review = get_members("Account_Admin", comp_no)
        hac_approve = get_members("Account_Head", comp_no)
        dac_approve = get_members("Account_Director", comp_no)
        acc_prove = get_members("Account_Record", comp_no) # AQUA ใช้ Account_Record
        #  acc_prove = get_members("Account_Admin", comp_no) #nation ใช้เป็น Account_Admin
        mpd_approve = get_members("Purchase_Admin", comp_no)
        mpd_review = get_members("Purchase_Manager", comp_no)
        dpd_approve = get_members("Purchase_Director", comp_no)

        pr_review = get_members("Purchase_Review", comp_no)
        hpd_review = get_members("Purchase_Head", comp_no)
        gm_approve = get_members("General_Manager", comp_no)

        fin_review = get_members("Finance_Admin", comp_no)

        print ("Name: ",name, " Comp: ", comp_no, " Approver: ", aup_approve)
        col1.append(name)
        col2.append(comp_no)
        if path_type == 'OF':
            col3.append(pr_review)
            col4.append(hpd_review)
            col5.append(gm_approve)                    
            col6.append(mpd_review)
        else:
            if path_type == 'GR':
                col3.append(acc_prove)
                col4.append(fin_review)
                col5.append(aup_approve)                    
            else:        
                col3.append(cmd_review)
                col4.append(acc_review)
                col5.append(hac_approve)        
                col6.append(aup_approve)
                col7.append(dac_approve)
        if path_type == 'PR':
            col8.append(mpd_approve)
            col9.append(hpd_review)
            col10.append(dpd_approve)
        else:
            col8.append(acc_prove)
        col11.append(special_limit)
if path_type == 'PR':        
    df = pd.DataFrame(list(zip(col1,col2,col3,col4,col5,col6,col7,col8,col9,col10,col11,)), columns=columns)    
else:
    if path_type == 'OF':
        df = pd.DataFrame(list(zip(col1,col2,col3,col4,col5,col6,col11,)), columns=columns)
    else:
        if path_type == 'GR':
            df = pd.DataFrame(list(zip(col1,col2,col3,col4,col5,col11,)), columns=columns)
        else:
            df = pd.DataFrame(list(zip(col1,col2,col3,col4,col5,col6,col7,col8,col11,)), columns=columns)
df.to_excel(writer, sheet_name='Sheet1', index=False)

writer.close()
conn.close()
print('Success Create: document_path.xlsx')


