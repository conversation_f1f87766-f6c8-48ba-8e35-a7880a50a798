import pyodbc
import openpyxl

mapping = {
    'The Nation': 'NT',
    'THINEW': 'THINEW',
    'KT': 'KT',
    'NTV': 'NTV',
    'NTV22': 'NTV22',
    'TMM': 'TMM',
    'SPN': 'SPN',
    'KM': 'KM',
    'Nation\n Story': 'NTS',
    'KSN': 'KSN',
    'POST': 'POST',
    'PEOPLE': 'PEOPLE'
    # เพิ่มการ mapping อื่น ๆ ตามต้องการ
}

# ฟังก์ชันเพื่อดึงค่าสีของเซลล์
def get_cell_color(cell):
    if cell.fill.start_color.index:
        return cell.fill.start_color.index
    return None

# prepare to insert
server = 'tcp:172.18.1.112'
database = 'DW'
username = 'powerbi'
password = 'Nation#2021'
cnxn = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER='+server+';DATABASE='+database+';UID='+username+';PWD='+ password)
cursor = cnxn.cursor()

not_found = []
not_match = []
pyear = 2024
# เปิดไฟล์ Excel
# wb = openpyxl.load_workbook('ราชการ รวมทุก BU 19.6.24.xlsx', data_only=True)
wb = openpyxl.load_workbook('ราชการ รวมทุก BU 19.6.24 พี่เก๋ up  5-7-67.xlsx', data_only=True)

# เลือกแผ่นงาน
sheet = wb['ราชการ ทุกBU(ห้ามแก้ไขไฟล์นี้)']


# ระบุตำแหน่งคอลัมน์ที่ต้องการอ่าน
cols_to_read = [1] + [2] + list(range(16, 27)) + [32]

map_brand = {}
# เริ่มอ่านค่า
for row_num, row in enumerate(sheet.iter_rows(min_row=3), start=3):
    if row_num == 3:
        print("Header Mapping")
        for idx, col in enumerate(cols_to_read):
            cell = sheet.cell(row=row_num, column=col)        
            cell_value = cell.value
            print ("Check Mapping: ", cell_value)
            if cell_value in mapping:
                map_brand[idx] = mapping[cell_value]
            else:                
                not_match.append(cell_value)
        print("Mapping: ", map_brand)
        continue

    if sheet.row_dimensions[row_num].hidden:
        continue  # ข้ามแถวที่ถูกซ่อน
    
    # ตรวจสอบค่าและพิมพ์ค่าสีของเซลล์
    # color = get_cell_color(row[1])
    # if color:
    #     print(f"Row {row_num}, {row[1].value} Color of cell in column 2: {color}")
    # else:
    #     print(f"Row {row_num}, {row[1].value} Cell in column 2 has no fill color")

    # ข้ามแถวที่มีเซลล์ในคอลัมน์ที่สองเป็นสีส้ม    
    # type_commit = 'Direct'
    # Check Type Agency or Direct
    # if get_cell_color(row[1]) == 5:
        # type_commit = 'Agency'
        # print("Skip Orange Cells")
        # continue

    # if 'Grand Total' in row:
    #     break
    if row[0].value == 'Grand Total':
        print("End of Grand Total")
        break

    row_values = []
    customer_name = ''
    sales_name = row[31].value
    # print (row[1].value, ':', row[31].value)
    for idx, col in enumerate(cols_to_read):
        cell = sheet.cell(row=row_num, column=col)        
        if idx == 1:
            customer_name = cell.value
            # print(type(customer_name) == str)
        else:
            # print (isinstance(cell.value, type(None)))
            if not(customer_name is None or isinstance(cell.value, type(None)) or cell.value is None):
                if idx in map_brand:
                    if float(cell.value) > 0.0:
                        print ("Name:", customer_name," Mapping: ", map_brand[idx], " Value: ", cell.value)
                        # Check ADRNO from Excel Customer Name
                        cursor.execute("select adrno from Government_Customer where LTRIM(RTRIM(excel_name)) = ?", customer_name.strip())
                        # row_adrno = cursor.fetchone()
                        rows_adrno = cursor.fetchall()
                        count_row = len(rows_adrno)
                        # พิมพ์จำนวน ADRNO ที่พบ
                        print(f"Found {len(rows_adrno)} ADRNO(s) for customer: {customer_name}")

                        if rows_adrno:
                            for row_adrno in rows_adrno:                                    
                                # Delete Previous data
                                del_count = cursor.execute("delete Government_Commitment where year_commit=? and adrno=? and Brand_Code=? and excel_name=? ",pyear, row_adrno.adrno, map_brand[idx], customer_name).rowcount
                                print('Government_Commitment Rows deleted: ' + str(del_count))                            
                                ins_count = cursor.execute("""
                                        insert into Government_Commitment (ADRNO, excel_name, Brand_Code, Commitment, Year_Commit, salesname)
                                        values(?, ?, ?, ?, ?, ?)""",row_adrno.adrno, customer_name, map_brand[idx], cell.value * 1000000 / count_row, pyear, sales_name).rowcount
                                print('Government_Commitment Rows inserted: ' + str(del_count))
                            cnxn.commit() 
                        else:
                            if customer_name not in not_found:                                
                                not_found.append(customer_name)                          

        cell = sheet.cell(row=row_num, column=col)
        row_values.append(cell.value)  # เพิ่มค่าจริงของเซลล์ลงในลิสต์
    print(row_values)
print ("Not Match Brand: ")
print (not_match)    
print ("Not Found List: ")
print (not_found)
# ปิด cursor และการเชื่อมต่อ
cursor.close()
cnxn.close()
