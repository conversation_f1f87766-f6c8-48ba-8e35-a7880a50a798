import pyodbc
import json
import os
from dotenv import load_dotenv
from tqdm import tqdm

def load_config(env_file):
    """Loads configuration from the specified .env file."""
    load_dotenv(dotenv_path=env_file)

def get_connection_string():
    """Builds the connection string from environment variables."""
    server = os.getenv('DB_SERVER')
    port = int(os.getenv('DB_PORT'))
    database = os.getenv('DB_DATABASE')
    username = os.getenv('DB_USER')
    password = os.getenv('DB_PASSWORD')
    return f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

def get_sql_list():
    """Defines the SQL queries for different document types."""
    return {
        "pv": "select * from pv_header where not status_code in ('PV_CANCELLED','PV_COMPLETE','PV_DISAPPROVE') ",
        "ca": "select * from ca_header where not status_code in ('CA_CANCELLED','CA_COMPLETE','CA_DISAPPROVE') ",
        "cl": "select * from cl_header where not status_code in ('CL_CANCELLED','CL_COMPLETE','CL_DISAPPROVE') ",
        "pr": "select * from pr_header where not status_code in ('PR_CANCELLED','PR_COMPLETE','PR_DISAPPROVE') ",
        "po": "select * from po_header where not status_code in ('PO_CANCELLED','PO_COMPLETE','PO_DISAPPROVE') ",
        "gr": "select * from gr_header where not status_code in ('GR_CANCELLED','GR_COMPLETE','GR_DISAPPROVE') "
    }

def get_employee_info():
    """Defines the employee information for replacement."""
    """
        "from_employee_id": '610011',  # วรางคณา กัลยาณประดิษฐ
        "to_employee_id": '550138',
        "to_employee_name": 'เนตรนภา ภูษิตตานนท์',
        "role_response": 'LOA'
    """
    return {
        "from_employee_id": '500043',  # อัมพร ขันตี
        "to_employee_id": '450071',
        "to_employee_name": 'บุญพา แซ่เบ้',
        "role_response": 'Account_Head'
    }

def update_document_path(cursor, row, from_employee_id, to_employee_id, to_employee_name, role_response, doc_type, debug_flag):
    """Updates the document_path field in the database."""
    change_flag = False
    document_path = json.loads(row.document_path)
    for status in document_path:
        if 'role_response' in status and status['role_response'] == role_response:
            for data in status['role_member']:
                if data['employee_id'] == from_employee_id:
                    data['employee_id'] = to_employee_id
                    data['employee_name'] = to_employee_name
                    change_flag = True

    if change_flag:
        json_string_updated = json.dumps(document_path, ensure_ascii=False)
        row.document_path = json_string_updated
        id_no = getattr(row, f"{doc_type}_id")
        sql = f"Update {doc_type}_header SET document_path = ? where {doc_type}_id = ?"
        params = (row.document_path, id_no)
        if not debug_flag:
            cursor.execute(sql, params)
            cursor.commit()
        print(f"{doc_type}: {id_no} Updated. Status: {row.status_code}, Special Path: {row.special_path}")
    else:
        print(f"{doc_type}: {getattr(row, f'{doc_type}_id')} Checked. Status: {row.status_code}, Special Path: {row.special_path}")

def update_allow_list(cursor, row, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag):
    """Updates the allow_list field in the database."""
    change_flag = False
    allow_list = json.loads(row.allow_list)
    if 'activity_list' in allow_list:
        for data in allow_list['activity_list']:
            if data['employee_id'] == from_employee_id:
                data['employee_id'] = to_employee_id
                data['employee_name'] = to_employee_name
                change_flag = True

    if change_flag:
        json_string_updated = json.dumps(allow_list, ensure_ascii=False)
        row.allow_list = json_string_updated
        id_no = getattr(row, f"{doc_type}_id")
        sql = f"Update {doc_type}_header SET allow_list = ? where {doc_type}_id = ?"
        params = (row.allow_list, id_no)
        if not debug_flag:
            cursor.execute(sql, params)
            cursor.commit()
        print(f"{doc_type}: {id_no} Updated. Status: {row.status_code}, Special Path: {row.special_path}")
    else:
        print(f"{doc_type}: {getattr(row, f'{doc_type}_id')} Checked. Status: {row.status_code}, Special Path: {row.special_path}")

def update_loa_list(cursor, row, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag):
    """Updates the loa_list field in the database."""
    change_flag = False
    allow_list = json.loads(row.allow_list)
    if 'loa_list' in allow_list:
        for data in allow_list['loa_list']:
            if data['employee_id'] == from_employee_id:
                data['employee_id'] = to_employee_id
                data['employee_name'] = to_employee_name
                change_flag = True

    if change_flag:
        json_string_updated = json.dumps(allow_list, ensure_ascii=False)
        row.allow_list = json_string_updated
        id_no = getattr(row, f"{doc_type}_id")
        sql = f"Update {doc_type}_header SET allow_list = ? where {doc_type}_id = ?"
        params = (row.allow_list, id_no)
        if not debug_flag:
            cursor.execute(sql, params)
            cursor.commit()
        print(f"{doc_type}: {id_no} Updated. Status: {row.status_code}, Special Path: {row.special_path}")
    else:
        print(f"{doc_type}: {getattr(row, f'{doc_type}_id')} Checked. Status: {row.status_code}, Special Path: {row.special_path}")

def process_rows(cursor, rows, from_employee_id, to_employee_id, to_employee_name, role_response, doc_type, debug_flag):
    """Processes each row from the database."""
    print(f'Begin transaction (Document_Path) [{from_employee_id} => {to_employee_id} ]')
    for row in tqdm(rows, desc=f"Processing {doc_type} Document Path", unit="record"):
        update_document_path(cursor, row, from_employee_id, to_employee_id, to_employee_name, role_response, doc_type, debug_flag)
    print('End transaction (Document Path))')

    print('Begin transaction (Activity List)')
    cursor.execute(f"{sql_list[doc_type]} and allow_list like ? ", ('%' + from_employee_id + '%',))
    rows = cursor.fetchall()
    for row in tqdm(rows, desc=f"Processing {doc_type} Activity List", unit="record"):
        update_allow_list(cursor, row, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag)
    print('End transaction (Activity List)')

    print('Begin transaction (LOA List)')
    cursor.execute(f"{sql_list[doc_type]} and allow_list like ? ", ('%' + from_employee_id + '%',))
    rows = cursor.fetchall()
    for row in tqdm(rows, desc=f"Processing {doc_type} LOA List", unit="record"):
        update_loa_list(cursor, row, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag)
    print('End transaction (LOA List)')

def process_document_type(conn, doc_type, query, from_employee_id, to_employee_id, to_employee_name, role_response, debug_flag):
    """Processes a specific document type."""
    print(f"begin check: {doc_type}")
    cursor = conn.cursor()
    sql = query + "and document_path like ? "
    cursor.execute(sql, ('%' + from_employee_id + '%',))
    rows = cursor.fetchall()
    process_rows(cursor, rows, from_employee_id, to_employee_id, to_employee_name, role_response, doc_type, debug_flag)
    cursor.close()

# Main execution
if __name__ == "__main__":
    debug_flag = True
    load_config('nation_prod.env')
    connection_string = get_connection_string()
    sql_list = get_sql_list()
    employee_info = get_employee_info()

    conn = pyodbc.connect(connection_string)
    print('Prepare to Replace Document_Path, Activity_List')

    # Add overall progress bar for document types
    print(f"Total document types to process: {len(sql_list)}")
    for doc_type, query in tqdm(sql_list.items(), desc="Processing Document Types", unit="type"):
        process_document_type(conn, doc_type, query, employee_info["from_employee_id"], employee_info["to_employee_id"], employee_info["to_employee_name"], employee_info["role_response"], debug_flag)

    conn.close()
    print('Success Debug:', debug_flag)
