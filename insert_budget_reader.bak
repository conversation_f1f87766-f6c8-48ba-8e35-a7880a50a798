import pyodbc 
import json
import pandas as pd

# Nation Server
# server = '172.16.1.172' 
# port = 1433
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Nation#2021' 

# Aqua Server Dev
# server = '52.76.139.120' 
# port = 1533
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Aqua#2022' 

# Aqua Server Production
server = '52.76.139.120' 
port = 1433
database = 'workflow' 
username = 'sys_wkf' 
password = 'Aqua#2022' 

debug_flag = False

# Read budget File
file_path = 'C:/FileHistory/WorkSpace/Working_Temp/AQUA/Nomi/Budget NMM 20231129 BD use.xlsx'  # แทนที่ด้วยที่อยู่ของไฟล์ Excel ของคุณ

emp_reader = '{"employee_id":"660106","username":"monthirm.w","fullname_en":"Monthirm Wonthira","fullname_th":"มณฑิรา วงษ์ตา","bu_no":"OPO         ","bu_name":"Business Development","department":"Business Development","comp_no":"00007","comp_code":"NOMI","position":"Admin","wf_level":"0","email":"<EMAIL>","status":"1","role_code":"monitor","employee_image":"https:\\/\\/dev-wf-doc.aquacorp.co.th\\/emppic\\/660106.jpg"}'

# อ่านข้อมูลจาก sheet แรกและ column แรก
dataframe = pd.read_excel(file_path, sheet_name=0, usecols=[0])

# แปลงข้อมูลเป็น string ที่คั่นด้วย comma
comma_separated_string = ','.join(dataframe.iloc[:, 0].astype(str))

# แสดง string ที่ได้
# print(comma_separated_string)

connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

conn = pyodbc.connect(connection_string)

cursor = conn.cursor()
sql = 'select * from budget where budget_id in (' + comma_separated_string + ') and (reader is null or not reader like \'%660106%\') '

print(sql)

cursor.execute(sql)
rows = cursor.fetchall()
for row in rows:
    change_flag = False
    # print(row.pv_id, ": ", end="")
    # print('Old Value: ', row.reader)
    if row.reader:        
        reader_value = ''
        if row.reader != '':
            reader_value = row.reader[:-1] + ',' + emp_reader + ']'            
        else:        
            reader_value = '[' + emp_reader + ']'
    else:
        reader_value = '[' + emp_reader + ']'
    # print('New Value: ', reader_value)
    sql = 'update budget set reader = ? where budget_id = ? '
    params = (reader_value, row.budget_id)
    print(sql, params)
    if not debug_flag:
        cursor.execute(sql, params)
        conn.commit()        


conn.close()
print('Success.')