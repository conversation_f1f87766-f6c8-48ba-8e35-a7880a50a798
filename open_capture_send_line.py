from selenium import webdriver
from pyautogui import screenshot
import ait
import pyperclip

# ตั้งค่าข้อมูลการเข้าสู่ระบบ
username = "wisarut"
password = "Survival90"

# เปิดเว็บไซต์ Power BI
driver = webdriver.Chrome()
driver.get("https://powerbi.nationgroup.com/Reports/powerbi/Billing-Credit/Aging_Report")

# กรอกชื่อผู้ใช้ด้วย AutoIt
ait.press('\t')
ait.write(username)
ait.press('\t')
ait.write(password)
ait.press('\t','\n')

# รอจนหน้าเว็บโหลดเสร็จ
driver.implicitly_wait(40)

# แคปเจอร์หน้าจอ
# screenshot("screenshot.png")
driver.save_screenshot("screenshot.png")

# ปิดเว็บไซต์
driver.quit()
