import pyodbc
import json
import pandas as pd
from dotenv import load_dotenv
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tqdm import tqdm

def load_config():
    """Load environment configuration"""
    # load_dotenv('fab_prod.env')
    load_dotenv('fab_dev.env')
    # load_dotenv('aqua_prod.env')

def get_connection_string():
    """Build database connection string"""
    server = os.getenv('DB_SERVER')
    port = int(os.getenv('DB_PORT'))
    database = os.getenv('DB_DATABASE')
    username = os.getenv('DB_USER')
    password = os.getenv('DB_PASSWORD')
    return f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

def select_excel_file():
    """ให้ผู้ใช้เลือกไฟล์ Excel"""
    root = tk.Tk()
    root.withdraw()  # ซ่อนหน้าต่างหลัก

    file_path = filedialog.askopenfilename(
        title="เลือกไฟล์งบประมาณ Excel",
        filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
    )

    root.destroy()
    return file_path

def get_excel_sheets(file_path):
    """อ่านรายชื่อ sheet ทั้งหมดในไฟล์ Excel"""
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def select_sheet(sheet_names):
    """ให้ผู้ใช้เลือก sheet"""
    if not sheet_names:
        return None

    print("\nรายชื่อ Sheet ที่มีในไฟล์:")
    for i, sheet in enumerate(sheet_names, 1):
        print(f"{i}. {sheet}")

    while True:
        try:
            choice = input(f"\nเลือก Sheet (1-{len(sheet_names)}): ")
            choice_num = int(choice)
            if 1 <= choice_num <= len(sheet_names):
                return sheet_names[choice_num - 1]
            else:
                print(f"กรุณาเลือกตัวเลขระหว่าง 1-{len(sheet_names)}")
        except ValueError:
            print("กรุณาใส่ตัวเลขที่ถูกต้อง")

def read_excel_sample(file_path, sheet_name, sample_rows=5):
    """อ่านข้อมูลตัวอย่างจาก Excel"""
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=sample_rows)
        return df
    except Exception as e:
        print(f"Error reading Excel data: {e}")
        return None

def display_sample_data(df):
    """แสดงข้อมูลตัวอย่าง"""
    if df is None or df.empty:
        print("ไม่มีข้อมูลในไฟล์")
        return

    print(f"\n{'='*80}")
    print(f"ข้อมูลตัวอย่าง {len(df)} บรรทัดแรก:")
    print(f"{'='*80}")
    print(f"จำนวนคอลัมน์: {len(df.columns)}")
    print(f"จำนวนแถว: {len(df)}")
    print(f"{'='*80}")

    # แสดงชื่อคอลัมน์
    print("คอลัมน์ที่มีในไฟล์:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i}. {col}")

    print(f"\n{'='*80}")
    print("ข้อมูลตัวอย่าง:")
    print(f"{'='*80}")

    # แสดงข้อมูลในรูปแบบที่อ่านง่าย
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 50)

    print(df.to_string(index=True))

    print(f"\n{'='*80}")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 โปรแกรมอัพเดทงบประมาณ")
    print("=" * 50)

    # โหลดการตั้งค่า
    load_config()

    # ให้ผู้ใช้เลือกไฟล์ Excel
    print("1. เลือกไฟล์ Excel งบประมาณ...")
    file_path = select_excel_file()

    if not file_path:
        print("❌ ไม่ได้เลือกไฟล์")
        return

    print(f"✅ เลือกไฟล์: {os.path.basename(file_path)}")

    # อ่านรายชื่อ sheet
    print("\n2. อ่านรายชื่อ Sheet...")
    sheet_names = get_excel_sheets(file_path)

    if not sheet_names:
        print("❌ ไม่สามารถอ่านไฟล์ Excel ได้")
        return

    print(f"✅ พบ {len(sheet_names)} Sheet")

    # ให้ผู้ใช้เลือก sheet
    print("\n3. เลือก Sheet...")
    selected_sheet = select_sheet(sheet_names)

    if not selected_sheet:
        print("❌ ไม่ได้เลือก Sheet")
        return

    print(f"✅ เลือก Sheet: {selected_sheet}")

    # อ่านข้อมูลตัวอย่าง
    print("\n4. อ่านข้อมูลตัวอย่าง...")
    sample_df = read_excel_sample(file_path, selected_sheet)

    if sample_df is None:
        print("❌ ไม่สามารถอ่านข้อมูลได้")
        return

    # แสดงข้อมูลตัวอย่าง
    display_sample_data(sample_df)

    print("\n✅ เสร็จสิ้นการแสดงข้อมูลตัวอย่าง")

    # ถามผู้ใช้ว่าต้องการดำเนินการต่อหรือไม่
    proceed = input("\n🤔 ต้องการดำเนินการอัพเดทข้อมูลงบประมาณหรือไม่? (y/n): ").lower().strip()

    if proceed == 'y' or proceed == 'yes':
        print("\n5. เริ่มกระบวนการอัพเดทข้อมูล...")
        process_budget_update(file_path, selected_sheet)
    else:
        print("\n❌ ยกเลิกการอัพเดทข้อมูล")

def validate_excel_columns(df):
    """ตรวจสอบว่า Excel มีคอลัมน์ที่จำเป็นหรือไม่"""
    required_columns = ['comp_no', 'year', 'bu_code', 'account_code', 'amount']
    missing_columns = []

    for col in required_columns:
        if col not in df.columns:
            missing_columns.append(col)

    if missing_columns:
        print(f"❌ ไม่พบคอลัมน์ที่จำเป็น: {', '.join(missing_columns)}")
        print(f"📋 คอลัมน์ที่จำเป็น: {', '.join(required_columns)}")
        return False

    return True

def get_next_account_id(cursor, comp_no):
    """หา account_id ถัดไปสำหรับ comp_no ที่กำหนด"""
    try:
        cursor.execute("SELECT MAX(account_id) FROM budget WHERE comp_no = ?", (comp_no,))
        result = cursor.fetchone()
        max_id = result[0] if result[0] is not None else 0
        return max_id + 1
    except Exception as e:
        print(f"❌ Error getting next account_id for comp_no {comp_no}: {e}")
        return None

def check_existing_record(cursor, comp_no, year, bu_code, account_code):
    """ตรวจสอบว่ามีข้อมูลอยู่ในฐานข้อมูลแล้วหรือไม่"""
    try:
        sql = """
        SELECT account_id, amount
        FROM budget
        WHERE comp_no = ? AND [year] = ? AND bu_code = ? AND account_code = ?
        """
        cursor.execute(sql, (comp_no, year, bu_code, account_code))
        result = cursor.fetchone()
        return result
    except Exception as e:
        print(f"❌ Error checking existing record: {e}")
        return None

def update_budget_record(cursor, account_id, amount):
    """อัพเดทข้อมูลงบประมาณที่มีอยู่แล้ว"""
    try:
        sql = """
        UPDATE budget
        SET amount = ?, update_time = GETDATE()
        WHERE account_id = ?
        """
        cursor.execute(sql, (amount, account_id))
        return True
    except Exception as e:
        print(f"❌ Error updating record: {e}")
        return False

def insert_budget_record(cursor, account_id, comp_no, year, bu_code, bu_name, account_code, account_name, amount):
    """เพิ่มข้อมูลงบประมาณใหม่"""
    try:
        sql = """
        INSERT INTO budget (account_id, comp_no, [year], bu_code, bu_name, account_code, account_name, amount, status, update_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ACTIVE', GETDATE())
        """
        cursor.execute(sql, (account_id, comp_no, year, bu_code, bu_name, account_code, account_name, amount))
        return True
    except Exception as e:
        print(f"❌ Error inserting record: {e}")
        return False

def process_budget_update(file_path, sheet_name):
    """ประมวลผลการอัพเดทข้อมูลงบประมาณ"""
    try:
        # อ่านข้อมูลทั้งหมดจาก Excel
        print("📖 อ่านข้อมูลทั้งหมดจาก Excel...")
        df = pd.read_excel(file_path, sheet_name=sheet_name)

        # ตรวจสอบคอลัมน์ที่จำเป็น
        if not validate_excel_columns(df):
            return

        print(f"✅ พบข้อมูล {len(df)} แถว")

        # เชื่อมต่อฐานข้อมูล
        print("🔌 เชื่อมต่อฐานข้อมูล...")
        connection_string = get_connection_string()
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()

        # ตัวแปรสำหรับสถิติ
        updated_count = 0
        inserted_count = 0
        error_count = 0

        print("🔄 เริ่มประมวลผลข้อมูล...")

        # ประมวลผลแต่ละแถว
        for index, row in tqdm(df.iterrows(), total=len(df), desc="Processing Budget Data", unit="record"):
            try:
                # ดึงข้อมูลจาก Excel
                comp_no = str(row['comp_no']).strip()
                year = int(row['year'])
                bu_code = str(row['bu_code']).strip()
                account_code = str(row['account_code']).strip()
                amount = float(row['amount'])

                # ดึงข้อมูลเพิ่มเติมถ้ามี
                bu_name = str(row.get('bu_name', '')).strip() if 'bu_name' in row else ''
                account_name = str(row.get('account_name', '')).strip() if 'account_name' in row else ''

                # ตรวจสอบว่ามีข้อมูลอยู่แล้วหรือไม่
                existing_record = check_existing_record(cursor, comp_no, year, bu_code, account_code)

                if existing_record:
                    # อัพเดทข้อมูลที่มีอยู่
                    account_id = existing_record[0]
                    old_amount = existing_record[1]

                    if update_budget_record(cursor, account_id, amount):
                        updated_count += 1
                        print(f"🔄 Updated: {comp_no}-{year}-{bu_code}-{account_code} | Amount: {old_amount:,.2f} → {amount:,.2f}")
                    else:
                        error_count += 1

                else:
                    # เพิ่มข้อมูลใหม่
                    next_account_id = get_next_account_id(cursor, comp_no)

                    if next_account_id is None:
                        error_count += 1
                        continue

                    if insert_budget_record(cursor, next_account_id, comp_no, year, bu_code, bu_name, account_code, account_name, amount):
                        inserted_count += 1
                        print(f"➕ Inserted: {comp_no}-{year}-{bu_code}-{account_code} | Amount: {amount:,.2f} | Account ID: {next_account_id}")
                    else:
                        error_count += 1

            except Exception as e:
                error_count += 1
                print(f"❌ Error processing row {index + 1}: {e}")
                continue

        # Commit การเปลี่ยนแปลง
        conn.commit()
        cursor.close()
        conn.close()

        # แสดงสรุปผลลัพธ์
        print(f"\n{'='*60}")
        print("📊 สรุปผลการประมวลผล:")
        print(f"{'='*60}")
        print(f"✅ อัพเดทสำเร็จ: {updated_count:,} รายการ")
        print(f"➕ เพิ่มใหม่สำเร็จ: {inserted_count:,} รายการ")
        print(f"❌ ข้อผิดพลาด: {error_count:,} รายการ")
        print(f"📋 รวมทั้งหมด: {len(df):,} รายการ")
        print(f"{'='*60}")

        if error_count == 0:
            print("🎉 ประมวลผลเสร็จสิ้นโดยไม่มีข้อผิดพลาด!")
        else:
            print(f"⚠️  ประมวลผลเสร็จสิ้น แต่มีข้อผิดพลาด {error_count} รายการ")

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการประมวลผล: {e}")

if __name__ == "__main__":
    main()