import pyodbc 
import json
import pandas as pd
from dotenv import load_dotenv
import os

def read_excel_and_check_column(file_path):
    # อ่านข้อมูลจาก Excel file
    df = pd.read_excel(file_path, sheet_name='Sheet1', dtype='str')

    # ตรวจสอบว่า column แรกมีค่าหรือไม่
    if df.iloc[:, 0].notnull().all():
        print("Column แรกมีค่าทุกบรรทัด")
    else:
        print("มีบางบรรทัดใน column แรกที่มีค่าว่าง")

    return df

#
#  Load environment
# load_dotenv('fab_prod.env')
load_dotenv('fab_dev.env')
# # load_dotenv('aqua_prod.env')


# Setup connection
server = os.getenv('DB_SERVER')
port = int(os.getenv('DB_PORT'))
database = os.getenv('DB_DATABASE')
username = os.getenv('DB_USER')
password = os.getenv('DB_PASSWORD')

debug_flag = False

# prepare database connection
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'
conn = pyodbc.connect(connection_string)

comp_dict = {'AQU': '00001', 'TCD': '00002', 'MAN': '00003', 'PFA': '00004', 'AWH': '00005', 'NTF': '00006'
            ,'NMM':'00007', 'CPT':'00008', 'PTR':'00009', 'CLP':'00010', 'C30':'00011', 'CPH':'00012', 'CPC':'00013'
            , 'FAB':'00014'}
# Read budget File
# file_path = 'Budget_User_Template.xlsx'  
# file_path = 'Budget_User_Template_TCD_เพิ่มสิทธิ (HR).xlsx'
# file_path = 'AQUA_Budget_เพิ่มสิทธิ (HR) (1).xlsx'
# file_path = 'TCD_Budget_เพิ่มสิทธิ (HR) (1).xlsx'
# file_path = 'AQUA_Budget_เพิ่มสิทธิ (HR) #2.xlsx'
# file_path = 'TCD_Budget_เพิ่มสิทธิ (HR) #2.xlsx'
file_path = 'ไฟล์เพิ่มสิทธิ Budget (รวม) 20250116.xlsx'

emp_reader = '{"employee_id":"660106","username":"monthirm.w","fullname_en":"Monthirm Wonthira","fullname_th":"มณฑิรา วงษ์ตา","bu_no":"OPO         ","bu_name":"Business Development","department":"Business Development","comp_no":"00007","comp_code":"NOMI","position":"Admin","wf_level":"0","email":"<EMAIL>","status":"1","role_code":"monitor","employee_image":"https:\\/\\/dev-wf-doc.aquacorp.co.th\\/emppic\\/660106.jpg"}'

data_frame = read_excel_and_check_column(file_path)

for r_index, excel_row in data_frame.iterrows():
    username = excel_row['username'].lower()
    budget_comp_no = excel_row['comp_no']
    if comp_dict[excel_row['comp_no']]:
        budget_comp_no = comp_dict[excel_row['comp_no']]
    budget_year = excel_row['year']
    budget_bu_code = excel_row['bu_code']
    account_code = excel_row['account_code']
    subsidiary = '' if pd.isnull(excel_row['subsidiary']) else excel_row['subsidiary']

    print(f"Index: {r_index}: {username}, {budget_comp_no}, {budget_year}, {budget_bu_code}, {account_code}, {subsidiary}")

    sql = 'select top 1 employee_id, username, fullname_en, fullname_th, bu_no, bu_name, department, comp_no, comp_code, position, wf_level, email, status '
    sql += 'from user_profile where username = \'' + username + '\' '

    # print(sql)

    cursor = conn.cursor()
    cursor.execute(sql)
    user_data = cursor.fetchone()
    if user_data:
        # employee_id, username, fullname_en, fullname_th, bu_no, bu_name, department, comp_no, comp_code, position, wf_level, email, status
        employee_id = user_data.employee_id
        # username = user_data[1] 
        fullname_en = user_data.fullname_en 
        fullname_th = user_data.fullname_th 
        bu_no = user_data.bu_no 
        bu_name = user_data.bu_name
        department = user_data.department
        comp_no =  user_data.comp_no
        comp_code = user_data.comp_code
        position = user_data.position
        wf_level = str(user_data.wf_level)
        email = user_data.email
        status = '1' if user_data.status else '0'
        # comp_name_en = user_data.comp_name_en
        # comp_name_th = user_data.comp_name_th
        emp_reader = '{"employee_id":"' + employee_id +'","username":"'+username+'"'
        emp_reader += ',"fullname_en":"' + fullname_en +'","fullname_th":"'+ fullname_th +'"'
        emp_reader += ',"bu_no":"'+ bu_no +'","bu_name":"'+ bu_name +'","department":"'+ department +'"'
        emp_reader += ',"comp_no":"'+ comp_no +'","comp_code":"'+ comp_code +'","position":"'+ position+'"'
        emp_reader += ',"wf_level":"'+ wf_level +'","email":"'+ email +'","status":"'+ status +'"'
        # emp_reader += ',"comp_name_en":"' + comp_name_en +'","comp_name_th":"'+ comp_name_th +'"'
        emp_reader += ',"role_code":"user","employee_image":"https:\\/\\/dev-wf-doc.aquacorp.co.th\\/emppic\\/'+ employee_id +'.jpg"}'
        # print(emp_reader)        
        upd_sql = 'select top 1 * from budget where comp_no=\'' + budget_comp_no +'\' and [year]=\'' + budget_year + '\' '
        upd_sql += 'and bu_code=\'' + budget_bu_code + '\' and account_code=\'' + account_code + '\' '
        if subsidiary == '':
            upd_sql += 'and subsidiary is null '
        else:
            upd_sql += 'and subsidiary=\'' + subsidiary + '\' '            
        upd_sql += 'and (reader is null or not reader like \'%'+ employee_id +'%\') '        
        # print(upd_sql)
        # print(f"check: {budget_comp_no} {budget_year} {budget_bu_code} {account_code} {subsidiary}")
        upd_cursor = conn.cursor()
        upd_cursor.execute(upd_sql)
        budget_data = upd_cursor.fetchone()
        if budget_data:
            # print(upd_sql)
            print(f"update: {budget_comp_no} {budget_year} {budget_bu_code} {account_code} {subsidiary}")
            if budget_data.reader:        
                reader_value = ''
                if budget_data.reader != '':
                    reader_value = budget_data.reader[:-1] + ',' + emp_reader + ']'            
                else:        
                    reader_value = '[' + emp_reader + ']'
            else:
                reader_value = '[' + emp_reader + ']'
            # print('New Value: ', reader_value)  
            upd_sql = 'update budget set reader = ? where budget_id = ? '
            params = (reader_value, budget_data.budget_id)
            # print(sql, params)
            if not debug_flag:
                upd_cursor.execute(upd_sql, params)
                conn.commit() 
            upd_cursor.close()                
        else:
            print(f"check: {budget_comp_no} {budget_year} {budget_bu_code} {account_code} {subsidiary}")
    else:
        print("No User data found.")
    cursor.close()


conn.close()
print('Success. Debug=' + str(debug_flag))