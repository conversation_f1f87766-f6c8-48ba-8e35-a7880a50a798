import pyodbc
import json
import pandas as pd
from dotenv import load_dotenv
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tqdm import tqdm

def load_config():
    """Load environment configuration"""
    # load_dotenv('fab_prod.env')
    load_dotenv('fab_dev.env')
    # load_dotenv('aqua_prod.env')

def get_connection_string():
    """Build database connection string"""
    server = os.getenv('DB_SERVER')
    port = int(os.getenv('DB_PORT'))
    database = os.getenv('DB_DATABASE')
    username = os.getenv('DB_USER')
    password = os.getenv('DB_PASSWORD')
    return f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

def select_excel_file():
    """ให้ผู้ใช้เลือกไฟล์ Excel"""
    root = tk.Tk()
    root.withdraw()  # ซ่อนหน้าต่างหลัก

    file_path = filedialog.askopenfilename(
        title="เลือกไฟล์งบประมาณ Excel",
        filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
    )

    root.destroy()
    return file_path

def get_excel_sheets(file_path):
    """อ่านรายชื่อ sheet ทั้งหมดในไฟล์ Excel"""
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def select_sheet(sheet_names):
    """ให้ผู้ใช้เลือก sheet"""
    if not sheet_names:
        return None

    print("\nรายชื่อ Sheet ที่มีในไฟล์:")
    for i, sheet in enumerate(sheet_names, 1):
        print(f"{i}. {sheet}")

    while True:
        try:
            choice = input(f"\nเลือก Sheet (1-{len(sheet_names)}): ")
            choice_num = int(choice)
            if 1 <= choice_num <= len(sheet_names):
                return sheet_names[choice_num - 1]
            else:
                print(f"กรุณาเลือกตัวเลขระหว่าง 1-{len(sheet_names)}")
        except ValueError:
            print("กรุณาใส่ตัวเลขที่ถูกต้อง")

def read_excel_sample(file_path, sheet_name, sample_rows=5):
    """อ่านข้อมูลตัวอย่างจาก Excel"""
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=sample_rows)
        return df
    except Exception as e:
        print(f"Error reading Excel data: {e}")
        return None

def display_sample_data(df):
    """แสดงข้อมูลตัวอย่าง"""
    if df is None or df.empty:
        print("ไม่มีข้อมูลในไฟล์")
        return

    print(f"\n{'='*80}")
    print(f"ข้อมูลตัวอย่าง {len(df)} บรรทัดแรก:")
    print(f"{'='*80}")
    print(f"จำนวนคอลัมน์: {len(df.columns)}")
    print(f"จำนวนแถว: {len(df)}")
    print(f"{'='*80}")

    # แสดงชื่อคอลัมน์
    print("คอลัมน์ที่มีในไฟล์:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i}. {col}")

    print(f"\n{'='*80}")
    print("ข้อมูลตัวอย่าง:")
    print(f"{'='*80}")

    # แสดงข้อมูลในรูปแบบที่อ่านง่าย
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 50)

    print(df.to_string(index=True))

    print(f"\n{'='*80}")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 โปรแกรมอัพเดทงบประมาณ")
    print("=" * 50)

    # โหลดการตั้งค่า
    load_config()

    # ให้ผู้ใช้เลือกไฟล์ Excel
    print("1. เลือกไฟล์ Excel งบประมาณ...")
    file_path = select_excel_file()

    if not file_path:
        print("❌ ไม่ได้เลือกไฟล์")
        return

    print(f"✅ เลือกไฟล์: {os.path.basename(file_path)}")

    # อ่านรายชื่อ sheet
    print("\n2. อ่านรายชื่อ Sheet...")
    sheet_names = get_excel_sheets(file_path)

    if not sheet_names:
        print("❌ ไม่สามารถอ่านไฟล์ Excel ได้")
        return

    print(f"✅ พบ {len(sheet_names)} Sheet")

    # ให้ผู้ใช้เลือก sheet
    print("\n3. เลือก Sheet...")
    selected_sheet = select_sheet(sheet_names)

    if not selected_sheet:
        print("❌ ไม่ได้เลือก Sheet")
        return

    print(f"✅ เลือก Sheet: {selected_sheet}")

    # อ่านข้อมูลตัวอย่าง
    print("\n4. อ่านข้อมูลตัวอย่าง...")
    sample_df = read_excel_sample(file_path, selected_sheet)

    if sample_df is None:
        print("❌ ไม่สามารถอ่านข้อมูลได้")
        return

    # แสดงข้อมูลตัวอย่าง
    display_sample_data(sample_df)

    print("\n✅ เสร็จสิ้นการแสดงข้อมูลตัวอย่าง")

if __name__ == "__main__":
    main()