import csv
import xml.etree.ElementTree as ET


def csv_to_xml(csv_file, xml_file, root_element_name, row_element_name):
    # อ่านไฟล์ CSV
    with open(csv_file, 'r') as file:
        csv_data = csv.reader(file)
        header = next(csv_data)  # อ่านหัวข้อคอลัมน์

        # สร้าง XML Element Tree
        root = ET.Element(root_element_name)

        # สร้าง XML Element สำหรับแต่ละแถวข้อมูลใน CSV
        for row in csv_data:
            row_element = ET.SubElement(root, row_element_name)

            # สร้าง XML Element สำหรับแต่ละคอลัมน์ในแถว
            for idx, value in enumerate(row):
                column_name = header[idx]
                column_element = ET.SubElement(row_element, column_name)
                column_element.text = value

    # สร้าง XML file
    tree = ET.ElementTree(root)
    tree.write(xml_file)


# เรียกใช้งานฟังก์ชันเพื่อแปลงไฟล์ CSV เป็น XML
csv_to_xml('data.csv', 'data.xml', 'data', 'row')
