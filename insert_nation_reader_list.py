import pyodbc
import json
import os
from dotenv import load_dotenv

def load_config(env_file):
    """Loads configuration from the specified .env file."""
    load_dotenv(dotenv_path=env_file)

def get_connection_string():
    """Builds the connection string from environment variables."""
    server = os.getenv('DB_SERVER')
    port = int(os.getenv('DB_PORT'))
    database = os.getenv('DB_DATABASE')
    username = os.getenv('DB_USER')
    password = os.getenv('DB_PASSWORD')
    return f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

def get_sql_list():
    """Defines the SQL queries for different document types."""
    return {
        "pv": "select pv_id, status_code, special_path, allow_list from pv_header where status_code='PV_COMPLETE' and pv_id = 6376 ",
        "ca": "select ca_id, status_code, special_path, allow_list from ca_header where status_code='CA_COMPLETE' and 1=0 ",
        "cl": "select cl_id, status_code, special_path, allow_list from cl_header where status_code='CL_COMPLETE' and 1=0 ",
        "pr": "select pr_id, status_code, special_path, allow_list from pr_header where status_code='PR_COMPLETE' and 1=0 ",
        "po": "select po_id, status_code, special_path, allow_list from po_header where status_code='PO_COMPLETE' and 1=0 ",
        "gr": "select gr_id, status_code, special_path, allow_list from gr_header where status_code='GR_COMPLETE' and 1=0 "
    }

def get_employee_info():
    """Defines the employee information for replacement."""
    return {
        "from_employee_id": '650202',  # คุณพันธิ์ชัย เหล่าพันธ์ถาวร
        "to_employee_id": '580406',
        "to_employee_name": 'คุณสุกัญญา จงกสิกิจ',
    }

def update_allow_list(cursor, row, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag):
    """Updates the allow_list field in the database."""
    change_flag = False
    allow_list_str = row.allow_list # Get the allow_list string from the row
    id_col_name = f"{doc_type}_id"
    id_no = getattr(row, id_col_name) # Use getattr to access the ID column dynamically

    # Check if allow_list_str is not None and is a valid JSON string
    if allow_list_str:
        try:
            allow_list = json.loads(allow_list_str)
            if 'reader_list' in allow_list:
                # Check if the 'to_employee_id' already exists in the reader_list
                if not any(reader['employee_id'] == to_employee_id for reader in allow_list['reader_list']):
                    # insert reader member if not already present
                    new_member = {'employee_id' : to_employee_id, 'employee_name' : to_employee_name }
                    allow_list['reader_list'].append(new_member)
                    change_flag = True
            else:
                 # If 'reader_list' does not exist, create it and add the new member
                 new_member = {'employee_id' : to_employee_id, 'employee_name' : to_employee_name }
                 allow_list['reader_list'] = [new_member]
                 change_flag = True

        except json.JSONDecodeError:
            print(f"Error decoding JSON for {doc_type}: {id_no}. Skipping update.")
            return # Skip this row if JSON is invalid
        except AttributeError as e:
             print(f"AttributeError processing {doc_type}: {id_no} - {e}. Skipping update.")
             return # Skip if other attribute errors occur during JSON processing
    else:
        # If allow_list is initially None or empty, create it
        new_member = {'employee_id' : to_employee_id, 'employee_name' : to_employee_name }
        allow_list = {'reader_list': [new_member]}
        change_flag = True


    if change_flag:
        json_string_updated = json.dumps(allow_list, ensure_ascii=False)
        # row.allow_list = json_string_updated # Don't update the row object directly
        sql = f"Update {doc_type}_header SET allow_list = ? where {id_col_name} = ?"
        params = (json_string_updated, id_no)
        if not debug_flag:
            try:
                cursor.execute(sql, params)
                # Commit should happen after processing all rows for a doc_type or at the end
                # cursor.commit() # Avoid committing inside the loop for performance
            except pyodbc.Error as ex:
                sqlstate = ex.args[0]
                print(f"Database error updating {doc_type}: {id_no}. SQLSTATE: {sqlstate}. Error: {ex}")
                # Consider rolling back or logging the error more formally
                return # Stop processing this row on error
        print(f"{doc_type}: {id_no} Updated. Status: {row.status_code}, Special Path: {row.special_path}")
    else:
        print(f"{doc_type}: {id_no} Checked (No changes needed or already present). Status: {row.status_code}, Special Path: {row.special_path}")

def process_rows(cursor, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag):
    """Processes each row from the database."""

    print(f'Begin transaction (Reader List) for {doc_type}')
    # Ensure the SQL selects the necessary columns (ID, allow_list, status_code, special_path)
    query = f"{sql_list[doc_type]} and allow_list like ? and not allow_list like ? "
    params = ('%' + from_employee_id + '%','%' + to_employee_id + '%')

    try:
        cursor.execute(query, params)
        rows = cursor.fetchall()
        if not rows:
            print(f"No rows found for {doc_type} matching criteria.")
            return # Exit if no rows to process

        for row in rows:
            update_allow_list(cursor, row, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag)

        # Commit after processing all rows for this document type if not in debug mode
        if not debug_flag:
            try:
                cursor.commit()
                print(f"Committed changes for {doc_type}.")
            except pyodbc.Error as ex:
                 print(f"Error committing changes for {doc_type}: {ex}")
                 # Consider rolling back if commit fails
                 try:
                     cursor.rollback()
                     print(f"Rolled back changes for {doc_type}.")
                 except pyodbc.Error as rb_ex:
                     print(f"Error rolling back changes for {doc_type}: {rb_ex}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database error executing query for {doc_type}. SQLSTATE: {sqlstate}. Error: {ex}")

    print(f'End transaction (Reader List) for {doc_type}')


def process_document_type(conn, doc_type, query, from_employee_id, to_employee_id, to_employee_name, debug_flag):
    """Processes a specific document type."""
    print(f"Begin check: {doc_type}")
    cursor = conn.cursor()
    try:
        process_rows(cursor, from_employee_id, to_employee_id, to_employee_name, doc_type, debug_flag)
    finally:
        cursor.close() # Ensure cursor is closed even if errors occur
        print(f"Cursor closed for {doc_type}.")

# Main execution
if __name__ == "__main__":
    debug_flag = True # Set to False to actually update the database
    env_file = 'nation_prod.env' # Or 'nation_prod.env'

    try:
        load_config(env_file)
        connection_string = get_connection_string()
        sql_list = get_sql_list()
        employee_info = get_employee_info()

        conn = pyodbc.connect(connection_string)
        print(f'Connected to database. Prepare to Insert Reader_List (Debug Mode: {debug_flag})')

        for doc_type, query in sql_list.items():
            process_document_type(conn, doc_type, query, employee_info["from_employee_id"], employee_info["to_employee_id"], employee_info["to_employee_name"], debug_flag)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database connection error: SQLSTATE {sqlstate}, Error: {ex}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print('Database connection closed.')
        print(f'Script finished. Debug Mode was: {debug_flag}')

