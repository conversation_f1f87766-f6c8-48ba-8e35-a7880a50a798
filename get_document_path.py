
import pyodbc
import json
import pandas as pd
from dotenv import load_dotenv
import os
from tqdm import tqdm

#
#  Load environment
load_dotenv('fab_prod.env')
# load_dotenv('aqua_prod.env')
# load_dotenv('fab_dev.env')

# Setup connection
server = os.getenv('DB_SERVER')
port = int(os.getenv('DB_PORT'))
database = os.getenv('DB_DATABASE')
username = os.getenv('DB_USER')
password = os.getenv('DB_PASSWORD')

connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'
conn = pyodbc.connect(connection_string)

# Parameters
show_credit_limit = True
show_special_limit = False
show_user_name = True

# Mapping
path_to_doc_type = {
    'PV': 'PV',
    'CA': 'CA',
    'CL': 'CL',
    'PR': 'PR',
    'PO': 'PO',
    'OF': 'OF',
    'RF': 'RF',
    'GR': 'GR',
}

# Load status chain
status_df = pd.read_sql("SELECT * FROM wf_status", conn)

def build_chain(df, doc_type):
    df = df[df['doc_type'] == doc_type].set_index('status_code')
    chain = []
    if df.empty:
        return chain
    start = df['status_level'].idxmin()
    visited = set()
    while start and start not in visited and start in df.index:
        visited.add(start)
        row = df.loc[start]
        chain.append((start, row['status_name'], row['role_response']))
        next_status = row['next_status']
        if pd.isna(next_status) or next_status not in df.index:
            break
        start = next_status
    return chain

def get_members(cursor, role_code, comp_no):
    cursor.execute("SELECT member FROM wf_roles_member WHERE role_code = ? AND comp_no = ?", (role_code, comp_no))
    row = cursor.fetchone()
    if row and row.member: # เพิ่มการตรวจสอบว่า row.member ไม่ใช่ None ด้วย
        try:
            members_data = json.loads(row.member)
            if 'member_list' not in members_data or not isinstance(members_data['member_list'], list):
                 print(f"Warning: Invalid 'member_list' structure for role {role_code}, comp {comp_no}")
                 return "" # หรือจัดการตามความเหมาะสม

            result = []
            for member in members_data['member_list']:
                username = ''
                # ใช้ .get() เพื่อป้องกัน KeyError ถ้า key ไม่มีอยู่
                name = member.get('employee_name', 'N/A') # ให้ค่า default ถ้าไม่มีชื่อ
                employee_id = member.get('employee_id')
                credit_limit = member.get('credit_limit')

                display_name = name # ใช้ตัวแปรใหม่เพื่อเก็บค่าที่จะแสดงผล

                if show_credit_limit and credit_limit is not None: # ตรวจสอบว่า credit_limit ไม่ใช่ None
                    try:
                        # ตรวจสอบว่าเป็นตัวเลขได้หรือไม่ก่อนแปลง
                        if isinstance(credit_limit, (int, float, str)) and str(credit_limit).replace('.', '', 1).isdigit():
                             display_name += f" (Cr: {int(float(credit_limit)):,})"
                        else:
                             print(f"Warning: Invalid credit_limit format '{credit_limit}' for {name}")
                    except ValueError:
                         print(f"Warning: Could not format credit_limit '{credit_limit}' for {name}")
                    except Exception as e:
                         print(f"Warning: Error formatting credit_limit for {name}: {e}")


                if show_user_name and employee_id:
                    try:
                        emp_cursor = conn.cursor()
                        emp_cursor.execute("SELECT username FROM user_profile WHERE employee_id = ?", (employee_id,))
                        emp_row = emp_cursor.fetchone()
                        if emp_row and emp_row.username: # ตรวจสอบว่าได้ username มาจริง
                            username = emp_row.username
                            display_name += f" ({username})" # เพิ่ม username เข้าไปใน display_name
                        emp_cursor.close()
                    except pyodbc.Error as db_err:
                        print(f"Database error fetching username for {employee_id}: {db_err}")
                    except Exception as e:
                        print(f"Error fetching username for {employee_id}: {e}")


                # --- เพิ่มบรรทัดนี้ ---
                result.append(display_name)
                # ---------------------

            return ', '.join(result)

        except json.JSONDecodeError:
            print(f"Error decoding JSON for role {role_code}, comp {comp_no}. Member data: {row.member}")
            return "" # หรือจัดการ error ตามที่ต้องการ
        except Exception as e:
            print(f"An unexpected error occurred in get_members for role {role_code}, comp {comp_no}: {e}")
            return ""

    return "" # กรณีไม่เจอข้อมูลใน wf_roles_member หรือ row.member เป็น None



def get_approver_by_role(conn, cursor, role_code, comp_no, json_data):
    approve_path = json_data.get('approve_path', [])
    result = []

    if role_code == 'RPT':
        if approve_path:
            ap = approve_path[0]
            name = ap['employee_name']
            employee_id = ap.get('employee_id')
            level_str = ''
            credit_str = ''
            username = ''

            if employee_id:
                emp_cursor = conn.cursor()
                emp_cursor.execute("SELECT wf_level, hr_level, username FROM user_profile WHERE employee_id = ?", (employee_id,))
                emp = emp_cursor.fetchone()
                if emp:
                    level_str = f"Lv: {emp.hr_level}-{emp.wf_level}" if emp.hr_level != emp.wf_level else f"Lv: {emp.wf_level}"
                    username = emp.username
                emp_cursor.close()
                # loa_cursor = conn.cursor()
                # loa_cursor.execute("""
                #     SELECT credit_limit FROM wf_loa
                #     WHERE comp_no = ? AND loa_type = 'RM'
                #     AND [level] = (SELECT wf_level FROM user_profile WHERE employee_id = ?)
                # """, (comp_no, employee_id))
                # loa = loa_cursor.fetchone()
                # if loa and loa.credit_limit:
                #     credit_str = f"Cr: {int(loa.credit_limit):,}"
                # loa_cursor.close()

            display = name
            # if show_credit_limit and credit_str:
            #     display += f" ({level_str} {credit_str})"
            if show_user_name and username:
                display += f" ({username})"
            result.append(display)
        return ", ".join(result)

    elif role_code == 'LOA':
        if len(approve_path) > 1:
            for ap in approve_path[1:]:
                employee_id = ap.get('employee_id')
                name = ap['employee_name']
                credit_str = ''
                username = ''
                if employee_id:
                    emp_cursor = conn.cursor()
                    emp_cursor.execute("SELECT username FROM user_profile WHERE employee_id = ?", (employee_id,))
                    emp = emp_cursor.fetchone()
                    if emp:
                        username = emp.username
                    emp_cursor.close()

                    loa_cursor = conn.cursor()
                    loa_cursor.execute("""
                        SELECT credit_limit FROM wf_loa
                        WHERE comp_no = ? AND loa_type = 'RM'
                        AND [level] = (SELECT wf_level FROM user_profile WHERE employee_id = ?)
                    """, (comp_no, employee_id))
                    loa = loa_cursor.fetchone()
                    if loa and loa.credit_limit:
                        credit_str = f"Cr: {int(loa.credit_limit):,}"
                    loa_cursor.close()

                display = name
                if show_credit_limit and credit_str:
                    display += f" ({credit_str})"
                if show_user_name and username:
                    display += f" ({username})"
                result.append(display)
        return " => ".join(result)

    else:
        return get_members(cursor, role_code, comp_no)

# Load special_path
cursor = conn.cursor()
cursor.execute("""
    SELECT * FROM special_path
    WHERE NOT comp_no LIKE '.%'
      AND comp_no IN (SELECT comp_no FROM company WHERE status = 1)
      AND NOT name LIKE '%-TEST'
""")
rows = cursor.fetchall()

# Prepare writer
print("Starting to process document paths...")
with pd.ExcelWriter("document_path_all.xlsx", engine="xlsxwriter") as writer:
    # Add progress bar for document types
    print(f"Total document types to process: {len(path_to_doc_type)}")
    for path_type, doc_type in tqdm(path_to_doc_type.items(), desc="Processing Document Types", unit="type"):
        chain = build_chain(status_df, doc_type)
        if not chain:
            continue

        if show_special_limit:
            columns = ['Name', 'Company'] + [s[1] for s in chain] + ['Special_limit']
        else:
            columns = ['Name', 'Company'] + [s[1] for s in chain]

        roles = [(s[1], s[2]) for s in chain if pd.notna(s[2])]

        data = []
        # Add progress bar for rows
        print(f"Processing {len(rows)} rows for {doc_type}...")
        for row in tqdm(rows, desc=f"Processing {doc_type} Rows", unit="row"):
            name = row.name
            comp_no = row.comp_no
            special_limit = ""
            if show_special_limit:
                if row.special_limit:
                    try:
                        special_limit = f"{int(row.special_limit):,}"
                    except:
                        special_limit = ""
            json_data = json.loads(row.json_data)
            row_data = [name, comp_no]

            # Process approvers with progress indication
            approver_dict = {}
            for step_name, role_code in roles:
                approver = get_approver_by_role(conn, cursor, role_code, comp_no, json_data)
                approver_dict[step_name] = approver

            for step in [s[1] for s in chain]:
                row_data.append(approver_dict.get(step, ""))

            if show_special_limit:
                row_data.append(special_limit)
                
            data.append(row_data)

        print(f"Creating DataFrame for {doc_type}...")
        df = pd.DataFrame(data, columns=columns)
        print(f"Writing {doc_type} to Excel...")
        df.to_excel(writer, sheet_name=path_type, index=False)
        print(f"Completed {doc_type}")

    print("Finalizing Excel file...")

conn.close()
print("✅ Exported document_path_all.xlsx successfully.")
