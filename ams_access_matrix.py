import pandas as pd
import mysql.connector

# Connection details for each database
def get_connection(db_name, server_name):
    return mysql.connector.connect(
        host=server_name,
        database=db_name,
        user='wisarut',
        password='Wis@ruT#38'
    )

def query_database(connection, query):
    return pd.read_sql(query, connection)

# Queries for each database and their respective servers
def fetch_data():
    queries = {
        'db_nationams': {
            'query': """
                SELECT employee_id, username, fullname, POSITION, company_no, company_name, bu_no, bu_name, status,
                       ROLE as db_nationams_ROLE, role_traffic_tv as db_nationams_role_traffic_tv, 
                       role_confirm_on_air as db_nationams_role_confirm_on_air, role_customer as db_nationams_role_customer
                FROM db_nationams.user
            """,
            'server': '**************'
        },
        'db_marcom': {
            'query': """
                SELECT employee_id, username, fullname, POSITION, company_no, company_code as company_name, bu_no, bu_name, 
                       <PERSON><PERSON><PERSON> as db_marcom_ROLE
                FROM db_marcom.user
            """,
            'server': '**************'
        },
        'db_ratecard': {
            'query': """
                SELECT employee_id, username, fullname, POSITION, company_no, company_name, bu_no, bu_name, 
                       role_rate_card as db_ratecard_role_rate_card
                FROM db_ratecard.user
            """,
            'server': '**************'
        },
        'db_quotation': {
            'query': """
                SELECT employee_id, username, fullname, POSITION, company_no, company_code as company_name, bu_no, bu_name, 
                       role as db_quotation_role
                FROM db_quotation.user
            """,
            'server': '192.168.52.101'
        },
        'db_advertising_order': {
            'query': """
                SELECT employee_id, username, name_th as fullname, POSITION, company_no, company_name, bu_no, bu_name, 
                       ROLE as db_advertising_order_ROLE
                FROM db_advertising_order.users
            """,
            'server': '192.168.52.103'
        },
        'db_photo_assignment_order': {
            'query': """
                SELECT employee_id, username, name_th as fullname, POSITION, company_no, company_name, bu_no, bu_name, 
                       ROLE as db_photo_assignment_order_ROLE
                FROM db_photo_assignment_order.users
            """,
            'server': '192.168.52.103'
        },
        'db_ob_studio': {
            'query': """
                SELECT employee_id, username, name_th as fullname, POSITION, company_no, bu_no, bu_name, 
                       ROLE as db_ob_studio_ROLE
                FROM db_ob_studio.users
            """,
            'server': '192.168.52.102'
        }
    }

    all_data = []
    for db, config in queries.items():
        with get_connection(db, config['server']) as conn:
            data = pd.read_sql(config['query'], conn)
            data['source_db'] = db  # Track the source database
            all_data.append(data)

    return all_data

def normalize_roles(df, db_name):
    if db_name == 'db_marcom':
        role_mapping = {0: 'ผู้แจ้ง', 1: 'หัวหน้าผู้แจ้ง', 2: 'ผู้ดำเนินการ', 3: 'หัวหน้าผู้ดำเนินการ', 4: 'หัวหน้า'}
        df[f'{db_name}_ROLE'] = df[f'{db_name}_ROLE'].map(role_mapping)
    return df

def process_and_merge_data(all_data):
    combined_data = pd.DataFrame()

    for df in all_data:
        source_db = df['source_db'].iloc[0]
        df = normalize_roles(df, source_db)
        df = df.drop(columns=['source_db'], errors='ignore')  # Remove 'source_db' column
        combined_data = pd.concat([combined_data, df], ignore_index=True)

    combined_data = combined_data.groupby('employee_id', as_index=False).first()
    return combined_data

def export_to_excel(combined_data, output_file):
    combined_data.to_excel(output_file, index=False, sheet_name="User Access Matrix")

if __name__ == "__main__":
    data = fetch_data()
    merged_data = process_and_merge_data(data)
    export_to_excel(merged_data, "AMS_User_Access_Matrix.xlsx")
    print("Excel file created: AMS_User_Access_Matrix.xlsx")
