import pyodbc 
import json

# Nation Server
server = '172.16.1.172' 
port = 1433
database = 'workflow' 
username = 'sys_wkf' 
password = 'Nation#2021' 

# Aqua Server Dev
# server = '52.76.139.120' 
# port = 1533
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Aqua#2022' 

# Aqua Server Production
# server = '52.76.139.120' 
# port = 1433
# database = 'workflow' 
# username = 'sys_wkf' 
# password = 'Aqua#2022' 

debug_flag = False

connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER=tcp:{server},{port};DATABASE={database};UID={username};PWD={password}'

conn = pyodbc.connect(connection_string)

print('Prepare to Replace Document_Path, Activity_List')

sql_list = {"pv" : "select * from pv_header where not pv_comp in ('00001','00018','00043') and not status_code in ('PV_CANCELLED','PV_COMPLETE','PV_DISAPPROVE') "
            ,"ca" : "select * from ca_header where not ca_comp in ('00001','00018','00043') and not status_code in ('CA_CANCELLED','CA_COMPLETE','CA_DISAPPROVE') "
            ,"cl" : "select * from cl_header where not cl_comp in ('00001','00018','00043') and not status_code in ('CL_CANCELLED','CL_COMPLETE','CL_DISAPPROVE') "
            ,"pr" : "select * from pr_header where not pr_comp in ('00001','00018','00043') and not status_code in ('PR_CANCELLED','PR_COMPLETE','PR_DISAPPROVE') "
            ,"po" : "select * from po_header where not po_comp in ('00001','00018','00043') and not status_code in ('PO_CANCELLED','PO_COMPLETE','PO_DISAPPROVE') "
            ,"gr" : "select * from gr_header where not gr_comp in ('00001','00018','00043') and not status_code in ('GR_CANCELLED','GR_COMPLETE','GR_DISAPPROVE') "
}
            # ,"of" : "select * from of_header where not status_code in ('OF_CANCELLED','OF_COMPLETE','OF_DISAPPROVE') "
            # ,"rf" : "select * from rf_header where not status_code in ('RF_CANCELLED','RF_COMPLETE','RF_DISAPPROVE') "

## Change Activity to from old employee_id to new employee_id

# Change Account Head
# from_employee_id = '660015'
# to_employee_id = '650002'
# to_employee_name = 'วรางคณา กัลยาณประดิษฐ'
# role_response = 'Account_Head'

# Change Purchase Head
# from_employee_id = '610011'
# to_employee_id = '460026'
# to_employee_name = 'ภิญญลักษณ์ ศิริศรีภานันท์'
# role_response = 'Account_Director'

# Change Purchase Head
from_employee_id = '460026'
to_employee_id = '610011'
to_employee_name = 'วรางคณา กัลยาณประดิษฐ'
role_response = 'Account_Director'

for doc_type, query in sql_list.items():
    print ("begin check: ",doc_type)
    # Begin Replace Document Path
    cursor = conn.cursor()
    # sql = """
    #     select * from pv_header where not status_code in ('PV_DRAFT','PV_CANCELLED','PV_COMPLETE','PV_DISAPPROVE')    
    #     and document_path like ? and pv_id = 2275
    # """
    sql = query + "and document_path like ? "
    params = ('%'+ from_employee_id +'%')

    cursor.execute(sql, params)
    rows = cursor.fetchall()
    print('Begin transaction (Document_Path)')
    for row in rows:
        change_flag = False
        # print(row.pv_id, ": ", end="")           
        document_path = json.loads(row.document_path)
        for status in document_path:
            # print(status)
            if 'role_response' in status:
                if status['role_response'] == role_response:
                    # role_member = json.loads(status['role_member'])
                    for data in status['role_member']:
                        # print(data)
                        if data['employee_id'] == from_employee_id:
                            data['employee_id'] = to_employee_id
                            data['employee_name'] = to_employee_name
                            change_flag = True
            # try:
            # except KeyError:
            #     print("no role_response!")
        # print(row.document_path)
        id_no = -1            
        sql = ''
        if doc_type == 'pv':
            id_no = row.pv_id
            sql = "Update pv_header SET document_path = ? where pv_id = ?"
        if doc_type == 'ca':
            id_no = row.ca_id
            sql = "Update ca_header SET document_path = ? where ca_id = ?"
        if doc_type == 'cl':
            id_no = row.cl_id
            sql = "Update cl_header SET document_path = ? where cl_id = ?"
        if doc_type == 'pr':
            id_no = row.pr_id
            sql = "Update pr_header SET document_path = ? where pr_id = ?"
        if doc_type == 'po':
            id_no = row.po_id
            sql = "Update po_header SET document_path = ? where po_id = ?"
        if doc_type == 'gr':
            id_no = row.gr_id
            sql = "Update gr_header SET document_path = ? where gr_id = ?"
        # if doc_type == 'of':
        #     id_no = row.of_id
        #     sql = "Update of_header SET document_path = ? where of_id = ?"
        # if doc_type == 'rf':
        #     id_no = row.rf_id
        #     sql = "Update rf_header SET document_path = ? where rf_id = ?"

        if change_flag:
            json_string_updated = json.dumps(document_path, ensure_ascii=False)
            row.document_path = json_string_updated                                            
            params = (row.document_path, id_no)
            # print(sql, params)
            if not debug_flag:
                cursor.execute(sql, params)
                conn.commit()
            print(doc_type, ": " , id_no, "Updated.")
        else:
            print(doc_type, ": ", id_no, "Checked.")
    cursor.close()
    print('End transaction (Document Path))')

    # Begin Replace Activity List
    cursor = conn.cursor()
    # sql = """
    #     select * from pv_header where not status_code in ('PV_DRAFT','PV_CANCELLED','PV_COMPLETE','PV_DISAPPROVE')    
    #     and allow_list like ? 
    # """
    sql = query + "and allow_list like ? "

    params = ('%'+ from_employee_id +'%')

    cursor.execute(sql, params)
    rows = cursor.fetchall()
    print('Begin transaction (Activity List)')
    for row in rows:
        change_flag = False
        # print(row.pv_id, ": ", end="")
        # print(row.allow_list)
        allow_list = json.loads(row.allow_list)
        # print(allow_list['activity_list'])
        for data in allow_list['activity_list']:
            # print(data)
            if data['employee_id'] == from_employee_id:
                data['employee_id'] = to_employee_id
                data['employee_name'] = to_employee_name
                change_flag = True
        id_no = -1
        sql = ''
        if doc_type == 'pv':
            id_no = row.pv_id
            sql = "Update pv_header SET allow_list = ? where pv_id = ?"
        if doc_type == 'ca':
            id_no = row.ca_id
            sql = "Update ca_header SET allow_list = ? where ca_id = ?"
        if doc_type == 'cl':
            id_no = row.cl_id
            sql = "Update cl_header SET allow_list = ? where cl_id = ?"
        if doc_type == 'pr':
            id_no = row.pr_id
            sql = "Update pr_header SET allow_list = ? where pr_id = ?"
        if doc_type == 'po':
            id_no = row.po_id
            sql = "Update po_header SET allow_list = ? where po_id = ?"
        if doc_type == 'gr':
            id_no = row.gr_id
            sql = "Update gr_header SET allow_list = ? where gr_id = ?"
        # if doc_type == 'of':
        #     id_no = row.of_id
        #     sql = "Update of_header SET allow_list = ? where of_id = ?"
        # if doc_type == 'rf':
        #     id_no = row.rf_id
        #     sql = "Update rf_header SET allow_list = ? where rf_id = ?"

        if change_flag:
            # json_string_updated = json.dumps(allow_list['activity_list'], ensure_ascii=False)
            json_string_updated = json.dumps(allow_list, ensure_ascii=False)
            row.allow_list = json_string_updated                            
            params = (row.allow_list, id_no)
            # print(sql, params)
            if not debug_flag:
                cursor.execute(sql, params)
                conn.commit()
            print(doc_type, ": ", id_no, "Updated.")
        else:
            print(doc_type, ": ", id_no, "Checked.")
    cursor.close()


conn.close()
print('Success')
