import json
import pandas as pd

# ข้อมูล JSON
data = {
    "cusNo": 300363,
    "cusName": "บริษัท โตโยต้า ทูโช (ไทยแลนด์) จำกัด (TOYOTA TSUSHO [THAILAND])",
    "agencyId": 100999,
    "agencyName": "บริษัท ออกัส กูด จำกัด",
    "eventCode": None,
    "eventName": None,
    "aoeId": None,
    "programId": 1764,
    "timeSlotId": 41,
    "bookingItemId": 1306,
    "quotationItemNo": "671100580",
    "payType": "Postpaid",
    "supplementId": None,
    "supplementName": None,
    "quotationId": 2809,
    "quotationType": "Agency",
    "mediaCode": "TV",
    "brandCode": "NTV",
    "ratecardId": 8886,
    "adTypeId": 5,
    "adTypeName": "Spot",
    "uom": "Second",
    "page": None,
    "channel": None,
    "placement": "6176",
    "adPosition": None,
    "col": None,
    "depth": None,
    "payload": {
        "advertising": "Traffic"
    },
    "booking": [
        {
            "requestDate": "2024-11-09",
            "quantity": 60,
            "price": 116.6667,
            "adBreakNo": 1,
            "adOrderNo": 0
        },
        {
            "requestDate": "2024-11-10",
            "quantity": 60,
            "price": 116.6667,
            "adBreakNo": 1,
            "adOrderNo": 0
        },
        {
            "requestDate": "2024-11-16",
            "quantity": 60,
            "price": 116.6667,
            "adBreakNo": 1,
            "adOrderNo": 0
        },
        {
            "requestDate": "2024-11-17",
            "quantity": 60,
            "price": 116.6667,
            "adBreakNo": 1,
            "adOrderNo": 0
        }
    ],
    "productName": "test1",
    "productCategory": "test1",
    "adLength": 60
}

# ดึงข้อมูล Header
header_data = {k: v if v is not None else "" for k, v in data.items() if k != "booking" and k != "payload"}
header_data["payloadAdvertising"] = data["payload"].get("advertising", "")

# ดึงข้อมูล Booking
booking_data = data["booking"]

# สร้าง DataFrame
header_df = pd.DataFrame([header_data])
booking_df = pd.DataFrame(booking_data)

# เขียนข้อมูลลง Excel
output_file = "Import_Template.xlsx"
with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
    header_df.to_excel(writer, sheet_name="Header", index=False)
    booking_df.to_excel(writer, sheet_name="Booking", index=False)

print(f"ไฟล์ {output_file} ถูกสร้างเรียบร้อยแล้ว!")
