import ldap3

# กำหนดค่าเชื่อมต่อกับ Active Directory
server = ldap3.Server('ldap://172.16.1.47', get_info=ldap3.ALL)
conn = ldap3.Connection(server, user='<EMAIL>', password='Survival90', auto_bind=True)

# กำหนด Base DN สำหรับการค้นหา
base_dn = 'DC=nationgroup,DC=com'

# กำหนดเงื่อนไขการค้นหา
search_filter = '(&(|(objectClass=user)(objectClass=group))(!(|(useraccountcontrol=546)(useraccountcontrol=514)(useraccountcontrol=66050)(useraccountcontrol=66082))))'
conn.search(base_dn, search_filter, attributes=['sAMAccountName', 'employeeID'])

for entry in conn.entries:
    username = entry.sAMAccountName
    employeeid = entry.employeeID
    str_password = 'P@ssw0rd'
    
    # สร้าง Connection ใหม่สำหรับการทดสอบ authenticate
    conn_auth = ldap3.Connection(server, user=f'{username}@nationgroup.com', password=str_password)
    
    try:
        if conn_auth.bind():
            print(f'Authentication successful for {username}, {employeeid}')
        # else:
        #     print(f'Authentication failed for {username}')    
    except ldap3.core.exceptions.LDAPInvalidCredentialsResult:
        print(f'Authentication failed for {username}')
    
    # ปิดการเชื่อมต่อสำหรับการทดสอบ authenticate
    conn_auth.unbind()

# ปิดการเชื่อมต่อ
conn.unbind()